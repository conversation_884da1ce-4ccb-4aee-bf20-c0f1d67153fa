<template>
  <el-dialog :visible.sync="visible" width="540px" :title="'更换班级'" :close-on-click-modal="false" destroy-on-close append-to-body :before-close="handleClose">
    <div v-loading="loading">
      <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="95px" style="padding-right: 20px">
        <el-form-item label="调班学员：" prop="studentCodeList">
          <el-select class="full-width" placeholder="请选择调班学员" :loading="studentLoading" v-model="form.studentCodeList" multiple clearable filterable>
            <el-option v-for="item in studentOptions" :key="item.studentCode" :label="item.studentNameCode" :value="item.studentCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称：" prop="newClassId">
          <el-select class="full-width" placeholder="请选择班级名称" :loading="classLoading" v-model="form.newClassId" clearable filterable>
            <el-option v-for="item in classOptions" :key="item.id" :label="item.classCountName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <div style="margin-left: 10px">
          <span>当更换后原班级剩余人数不足2人时，班级会自动删除</span>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import DispatchInProgress from '@/api/oneToManyClass/DispatchInProgress';

  export default {
    name: 'ChangeClassDialog',
    data() {
      return {
        form: {
          studentCodeList: '',
          newClassId: ''
        },
        rules: {
          studentCodeList: [{ required: true, message: '请选择调班学员', trigger: 'blur' }],
          newClassId: [{ required: true, message: '请选择班级名称', trigger: 'blur' }]
        },
        visible: false,
        studentOptions: [], // 获取学生数据
        classOptions: [], // 获取班级数据
        loading: false,
        studentLoading: false,
        classLoading: false,
        currentRow: [],
        type: ''
      };
    },
    methods: {
      open(row, type) {
        this.currentRow = row;
        this.type = type;
        this.visible = true;
        this.fetchStudentData();
        this.fetchClassData();
      },

      // 获取学生数据
      async fetchStudentData() {
        this.studentLoading = true;
        try {
          const res = await DispatchInProgress.getAdjustStudentList({
            classId: this.currentRow.id,
            classType: this.type
          });
          if (res && res.data) {
            this.studentOptions = res.data;
          } else {
            this.studentOptions = [];
          }
        } finally {
          this.studentLoading = false;
        }
      },

      // 获取班级数据
      async fetchClassData() {
        this.classLoading = true;
        try {
          const res = await DispatchInProgress.getAdjustClassList({
            transferOutClassId: this.currentRow.id,
            classType: this.type,
            curriculumId: this.currentRow.curriculumId
          });
          if (res && res.data) {
            this.classOptions = res.data;
          } else {
            this.classOptions = [];
          }
        } finally {
          this.classLoading = false;
        }
      },

      handleClose() {
        this.visible = false;
        this.resetForm();
      },
      handleSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true;
            let data = { ...this.form, oldClassId: this.currentRow.id, classType: this.type };
            DispatchInProgress.updateTransferClass(data)
              .then(() => {
                this.$emit('updateList');
                this.$message.success({
                  type: 'success',
                  message: '更改班级成功'
                });
                this.handleClose();
              })
              .finally(() => {
                this.loading = false;
              });
          }
        });
      },

      resetForm() {
        this.form = {
          studentCodeList: '',
          newClassId: ''
        };
        this.studentOptions = [];
        this.classOptions = [];
        this.currentRow = [];
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.clearValidate();
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .dialog-footer {
    margin: 20px 0 0 0;
    text-align: center;
  }
  ::v-deep .el-dialog__body {
    padding: 30px 20px 20px;
  }
</style>
