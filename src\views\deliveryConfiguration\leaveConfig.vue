<template>
  <div class="leave-count-config">
    <div class="content-wrapper">
      <div class="course-type-tabs">
        <el-radio-group v-model="activeTab">
          <el-radio-button :label="2">一对多试课</el-radio-button>
          <el-radio-button :label="1">一对多正课</el-radio-button>
        </el-radio-group>
      </div>
      <el-table v-loading="tableLoading" :data="coursesList" border :header-cell-style="{ background: '#f5f7fa' }" :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="cnName" align="center" label="课程类型" width="400" show-overflow-tooltip />
        <el-table-column prop="leaveNum" align="center" label="可请假次数">
          <template #default="{ row }">
            <el-input-number v-model="row.leaveNum" style="width: 200px" :min="0" controls-position="right" :precision="0" @change="handleCountChange(row)" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-wrapper">
      <DxFixedFooterButton>
        <el-button type="primary" @click="handleSave" :loading="saveLoading" :disabled="coursesList.length === 0">确定</el-button>
      </DxFixedFooterButton>
    </div>
  </div>
</template>

<script>
  import DxFixedFooterButton from '@/components/DxFixedFooterButton/index.vue';
  import { getCurriculumLeaveList, saveCurriculumLeaveConfig } from '@/api/systemConfig/leaveConfig';

  export default {
    name: 'leaveConfig',
    components: { DxFixedFooterButton },
    data() {
      return {
        activeTab: 1,
        tableLoading: false,
        saveLoading: false,
        coursesList: [],
        modifiedCourses: new Map()
      };
    },
    computed: {},
    mounted() {
      this.getCoursesList();
    },
    watch: {
      activeTab() {
        this.getCoursesList();
      }
    },
    methods: {
      async getCoursesList() {
        this.tableLoading = true;
        try {
          const res = await getCurriculumLeaveList({ type: this.activeTab });
          this.coursesList = res.data || [];
          // 重置修改记录
          this.modifiedCourses.clear();
        } catch (e) {
          console.error('获取课程列表失败:', e);
        } finally {
          this.tableLoading = false;
        }
      },

      handleCountChange(row) {
        this.modifiedCourses.set(row.configState, {
          id: row.id,
          configState: row.configState,
          leaveNum: row.leaveNum || 0
        });
      },

      async handleSave() {
        if (this.coursesList.length === 0) return;

        try {
          this.saveLoading = true;
          const requestData = {
            type: this.activeTab,
            configList: this.coursesList.map((item) => {
              // 如果有修改的记录，使用修改后的值，否则使用原始值
              const modifiedItem = this.modifiedCourses.get(item.configState);
              return (
                modifiedItem || {
                  id: item.id,
                  configState: item.configState,
                  leaveNum: item.leaveNum
                }
              );
            })
          };

          await saveCurriculumLeaveConfig(requestData);
          this.$message.success('保存成功');
          await this.getCoursesList();
        } catch (e) {
          console.error('保存失败:', e);
          this.$message.error('保存失败');
        } finally {
          this.saveLoading = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .leave-count-config {
    display: flex;
    flex-direction: column;
    padding-bottom: 80px;
    height: calc(100vh - 84px);
  }

  .content-wrapper {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .footer-wrapper {
    flex-shrink: 0;
  }

  .course-type-tabs {
    margin-bottom: 20px;
  }
</style>
