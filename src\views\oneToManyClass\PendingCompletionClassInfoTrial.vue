<!-- 交付中心-一对多学员管理-待完善上课信息表-试课 -->
<template>
  <div>
    <!-- 查询栏 -->
    <el-card class="frame" shadow="never">
      <el-form label-width="120px" ref="searchNum" :model="searchNum" :inline="true">
        <el-row style="display: flex; flex-wrap: wrap; align-items: center">
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="联系方式:" prop="mobile">
              <el-input v-model="searchNum.mobile" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="是否有推荐人:" prop="referrer">
              <el-select v-model="searchNum.referrer" clearable placeholder="请选择">
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" placeholder="请选择" clearable>
                <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-button type="primary" icon="el-icon-search" style="margin-bottom: 22px; margin-left: 10vw" size="mini" @click="search">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="reset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="tableList" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 200 : ''"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="openClassInfoFormDialog(row)">填写试课单</el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />

    <!-- 试课单弹窗 -->
    <el-dialog
      :visible.sync="classInfoFormDialogVisible"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      title="填写试课单"
      :before-close="classInfoFormClose"
      :close-on-click-modal="false"
    >
      <el-form ref="classInfoForm" :rules="classInfoFormRules" :model="classInfoForm" label-width="120px">
        <el-form-item label="课程类型" prop="curriculumName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="classInfoForm.curriculumName" disabled />
        </el-form-item>
        <el-form-item label="试课人姓名" prop="expName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="classInfoForm.expName" />
        </el-form-item>
        <el-form-item label="试课人手机号" prop="expPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="classInfoForm.expPhone" />
        </el-form-item>
        <div v-if="MATHCurriculumCodeArr.indexOf(classInfoForm.curriculumCode) != -1">
          <el-form-item label="版本" prop="versionId" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select v-model="classInfoForm.versionId" placeholder="请选择">
              <el-option v-for="item in classInfoOptionForm.versionList" :key="item.id" :label="item.versionName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科" prop="disciplineId" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select :disabled="!classInfoForm.versionId" v-model="classInfoForm.disciplineId" placeholder="请先选择版本">
              <el-option v-for="item in classInfoOptionForm.subList" :key="item.id" :label="item.nodeName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="当前年级" prop="gradeId" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select :disabled="!(classInfoForm.versionId && classInfoForm.disciplineId)" v-model="classInfoForm.gradeId" placeholder="请先选择学科">
              <el-option v-for="item in classInfoForm.gradeList" :key="item.id" :label="item.nodeName" :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="上课意向" prop="intentionClass" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select v-model="classInfoForm.intentionClass" placeholder="请选择上课意向">
              <el-option v-for="item in classInfoOptionForm.intentionList" :key="item.id" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="试课人分数" prop="scoreTest" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select v-model="classInfoForm.scoreTest" placeholder="请选择试课人分数">
              <el-option v-for="item in classInfoOptionForm.trialScoreList" :key="item.id" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="试课人年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-select v-model="classInfoForm.grade" placeholder="请选择">
            <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学员性别" prop="sex" :inline="true" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="classInfoForm.sex">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上课时间" prop="classTimeConfigId" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <BaseClassStudyTimeSelect
            v-model="classInfoForm.classTimeConfigId"
            :curriculumId="classInfoForm.curriculumId"
            :grade="classInfoForm.grade"
            isExperience
            @change="henbdleClassTimeConfigIdChange"
          ></BaseClassStudyTimeSelect>
        </el-form-item>
        <el-form-item label="学员所在区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-cascader v-model="classInfoForm.address" :options="regionData" @change="handleClassInfoDialogAddressChange" />
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button style="width: 70px; margin-right: 1.5vw" @click="classInfoFormClose">取消</el-button>
        <el-button type="primary" :loading="submitClassInfoFormLoading" class="login-btn" style="width: 70px; margin-right: 1.5vw" @click="submitClassInfoForm">完成</el-button>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import BaseClassStudyTimeSelect from './components/pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';
  import { getPendingCompletionClassInfoTrial, updateClassInfoTrial } from '@/api/oneToManyClass/pendingCompletionClassInfo';
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import { GradeType, selectVersionInfo, selectTreeVersion, queryStudentExperienceDetail, saveStudentExperience } from '@/api/studentClass/changeList';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { CodeToText, regionData } from 'element-china-area-data';
  import { log } from 'bpmn-js-token-simulation';
  import { isvalidPhone } from '@/utils/validate';
  import { MATHCurriculumCodeArr } from '@/utils/constants.js';
  export default {
    name: 'PendingCompletionClassInfoTrial',
    components: {
      HeaderSettingsDialog,
      BaseClassStudyTimeSelect
    },
    data() {
      return {
        MATHCurriculumCodeArr,
        screenWidth: window.screen.width,

        // 查询栏
        searchNum: {},
        curriculumList: [],

        // 列表属性弹框
        HeaderSettingsStyle: false,
        headerSettings: [
          { name: '姓名', value: 'name' },
          { name: '联系方式', value: 'mobile' },
          { name: '操作', value: 'operate' },
          { name: '课程类型', value: 'curriculumName' },
          { name: '阿拉鼎单号', value: 'orderNo' },
          { name: '下单时间', value: 'createTime' },
          { name: '推荐人姓名', value: 'referrerName' },
          { name: '推荐人联系方式', value: 'referrerPhone' }
        ],
        tableHeaderList: [],

        // 表格数据
        tableLoading: false,
        tableList: [],

        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },

        //上课信息对接表信息
        classInfoFormDialogVisible: false,
        classInfoOptionForm: {
          versionList: [], // 版本
          subList: [], // 学科
          gradeList: [], // 年级
          intentionList: [
            { label: '超前学习', value: '1' },
            { label: '复习巩固', value: '2' }
          ], // 上课意向
          trialScoreList: [
            { label: '60分以下', value: '1' },
            { label: '60~85分', value: '2' },
            { label: '85-100分', value: '3' },
            { label: '100分以上', value: '4' },
            { label: '满分', value: '5' }
          ] // 试课人分数
        },
        classInfoForm: {
          versionId: '', // 版本id
          disciplineId: '', // 学科id
          gradeId: '', // 年级id
          intentionClass: '', // 上课意向
          scoreTest: '' // 试课人分数
        },
        submitClassInfoFormLoading: false,
        levelId: '', //学科列表接口层级
        nodeLevel: 2, // 课程节点等级
        classInfoFormRules: {
          versionId: [{ required: true, message: '请选择版本', trigger: 'change' }],
          disciplineId: [{ required: true, message: '请选择学科', trigger: 'change' }],
          gradeId: [{ required: true, message: '请选择当前年级', trigger: 'change' }],
          intentionClass: [{ required: true, message: '请选择当前上课意向', trigger: 'change' }],
          scoreTest: [{ required: true, message: '请选择当前试课人分数', trigger: 'change' }],
          grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
          sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
          referrerPhone: [{ required: true, message: '请填写手机号', trigger: 'blur' }],
          classTimeConfigId: [{ required: true, message: '请选择上课时间', trigger: 'change' }],
          address: [{ required: true, message: '请选择区域', trigger: 'change' }],
          expName: [{ required: true, message: '请输入试课人姓名', trigger: 'change' }],
          expPhone: [
            { required: true, message: '请填写试课人手机号', trigger: 'change' },
            {
              validator: (rule, value, callback) => {
                if (value && !isvalidPhone(value)) {
                  callback(new Error('请输入正确的11位手机号码'));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ]
        },
        gradeList: [],
        regionData: regionData
      };
    },
    mounted() {
      this.getHeaderlist();
      this.getCurriculumList();
      this.getGradeList();
      this.getPendingCompletionClassInfoTrialList();
    },
    watch: {
      'classInfoForm.versionId': {
        handler(n, o) {
          console.log('🚀 ~ handler ~ n, o:', n, o);
          if (!n) return;
          //强制更新视图（避免 Vue 检测不到对象属性变化）
          if (this.classInfoForm.disciplineId) {
            this.$set(this.classInfoForm, 'disciplineId', '');
            this.classInfoForm.disciplineId = ''; //学科
          }
          // if (this.classInfoForm.gradeId) {
          this.$set(this.classInfoForm, 'gradeId', '');
          this.classInfoForm.gradeId = ''; //当前年级
          // }
          this.getSubjectList(n);
        },
        immediate: true,
        deep: false
        // 仅当 versionId 的值真正变化时，才触发清空学科和年级的逻辑。移除 deep: true，避免监听对象的所有属性变化。
      },
      'classInfoForm.disciplineId': {
        handler(newVal, oldVal) {
          // console.log('🚀 ~ handler ~ disciplineId newVal, oldVal:', newVal, oldVal);
          if (!newVal) return;
          if (newVal !== oldVal) {
            this.levelId = newVal; // 更新 levelId
            this.classInfoForm.gradeId = ''; // 清空当前年级
            this.getSubjectList(this.classInfoForm.versionId);
          }
        },
        immediate: false,
        deep: false
      }
    },

    methods: {
      // 获取课程类型下拉列表
      getCurriculumList() {
        getAllOneToManyCurriculumType().then((res) => {
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      // 获取版本列表
      async getVersionList(val) {
        console.log('传入的 curriculumId 是：', val);
        this.classInfoOptionForm.versionList = [];

        let params = {
          curriculumId: String(val),
          pageSize: 50,
          pageNum: 1
        };

        console.log('🚀 ~ getVersionList ~ params:', params);

        await selectVersionInfo(params).then((res) => {
          console.log(res.data, '版本');
          const versionList = res.data.map((item) => ({
            id: item.id,
            versionName: item.versionName
          }));
          this.classInfoOptionForm.versionList = versionList;
          console.log('🚀 ~ .then ~ this.classInfoForm版本:', this.classInfoOptionForm.versionList);
        });
      },
      // 学科
      async getSubjectList(val) {
        let params = {
          curriculumId: this.classInfoForm.curriculumId,
          nodeLevel: this.nodeLevel,
          versionId: val
        };
        await selectTreeVersion(params).then((res) => {
          this.parseChildList(res.data);
        });
      },

      parseChildList(data) {
        const subList = [];
        const gradeList = [];

        const traverse = (items, level) => {
          items.forEach((item) => {
            if (level === 1) {
              subList.push({
                id: item.id,
                nodeName: item.nodeName
              });
            } else if (level === 2) {
              let levelParentNodeId = item.parentNodeId;
              if (this.levelId == levelParentNodeId) {
                gradeList.push({
                  id: item.id,
                  nodeName: item.nodeName
                });
              }
            }
            // 如果有子节点，递归处理
            if (item.childList && item.childList.length > 0) {
              traverse(item.childList, level + 1);
            }
          });
        };

        // 从第一级开始递归
        traverse(data, 1);
        // 将结果赋值到对应的变量
        this.$set(this.classInfoOptionForm, 'gradeList', gradeList);
        this.$set(this.classInfoOptionForm, 'subList', subList);
      },

      // 获取年级下拉列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'PendingCompletionClassInfoTrial',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'PendingCompletionClassInfoTrial'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getPendingCompletionClassInfoTrialList();
      },
      // 重置
      reset() {
        this.searchNum = {};
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getPendingCompletionClassInfoTrialList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getPendingCompletionClassInfoTrialList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getPendingCompletionClassInfoTrialList();
      },
      // 获取待完善试课信息列表数据
      getPendingCompletionClassInfoTrialList() {
        this.tableLoading = true;
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getPendingCompletionClassInfoTrial(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data || [];
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableList = [];
            this.tableLoading = false;
          });
      },
      // 填写试课单
      openClassInfoFormDialog(row) {
        this.classInfoForm = {
          orderId: row.orderId,
          expName: row.name,
          expPhone: row.mobile,
          curriculumName: row.curriculumName,
          curriculumId: row.curriculumId,
          grade: '',
          sex: '',
          expectTime: '',
          province: '',
          city: '',
          area: '',
          referrerPhone: row.referrerPhone,
          referrerName: row.referrerName,
          classTimeConfigId: ''
        };
        this.$refs.classInfoForm?.resetFields();
        this.submitClassInfoFormLoading = false;
        this.classInfoFormDialogVisible = true;
        this.classInfoForm.curriculumId = row.curriculumId;
        this.classInfoForm.curriculumCode = row.curriculumCode;
        this.classInfoForm.orderNo = row.orderNo;
        this.getVersionList(row.curriculumId); //获取版本列表
      },
      // 处理上课时间配置id改变事件
      henbdleClassTimeConfigIdChange(obj) {
        this.classInfoForm.extendTime = `${obj.weekText} ${obj.startTime}-${obj.endTime}`;
      },
      handleClassInfoDialogAddressChange(value) {
        this.getCodeToText(null, value);
      },

      getCodeToText(codeStr, codeArray) {
        if (null === codeStr && null === codeArray) {
          return null;
        } else if (null === codeArray) {
          codeArray = codeStr.split(',');
        }
        if (!Array.isArray(codeArray)) {
          return null;
        }
        let area = [];
        switch (codeArray.length) {
          case 1:
            area.push(CodeToText[codeArray[0]]);
            break;
          case 2:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            break;
          case 3:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            area.push(CodeToText[codeArray[2]]);
            break;
          default:
            break;
        }
        this.classInfoForm.province = area[0];
        this.classInfoForm.city = area[1];
        this.classInfoForm.area = area[2];
        return area;
      },
      // 提交试课单
      submitClassInfoForm() {
        this.$refs.classInfoForm.validate((valid) => {
          if (valid) {
            const subminForm = {
              orderId: this.classInfoForm.orderId,
              orderNo: this.classInfoForm.orderNo,
              curriculumId: this.classInfoForm.curriculumId,
              curriculumName: this.classInfoForm.curriculumName,
              expName: this.classInfoForm.expName,
              sex: this.classInfoForm.sex,
              expPhone: this.classInfoForm.expPhone,
              expectTime: this.classInfoForm.expectTime,
              grade: this.classInfoForm.grade,
              classTimeConfigId: this.classInfoForm.classTimeConfigId,
              extendTime: this.classInfoForm.extendTime,
              province: this.classInfoForm.province,
              city: this.classInfoForm.city,
              area: this.classInfoForm.area,
              address: [...this.classInfoForm.address],
              referrerName: this.classInfoForm.referrerName,
              referrerPhone: this.classInfoForm.referrerPhone,
              versionId: this.classInfoForm.versionId,
              disciplineId: this.classInfoForm.disciplineId,
              gradeId: this.classInfoForm.gradeId,
              intentionClass: this.classInfoForm.intentionClass,
              scoreTest: this.classInfoForm.scoreTest
            };
            this.submitClassInfoFormLoading = true;
            if (this.MATHCurriculumCodeArr.indexOf(this.classInfoForm.curriculumCode) != -1) {
              saveStudentExperience(subminForm)
                .then(() => {
                  this.$message.success('操作成功');
                  this.getPendingCompletionClassInfoTrialList();
                  this.classInfoFormDialogVisible = false;
                  this.submitClassInfoFormLoading = false;
                })
                .catch(() => {
                  this.submitClassInfoFormLoading = false;
                });
            } else {
              updateClassInfoTrial(subminForm)
                .then(() => {
                  this.$message.success('操作成功');
                  this.getPendingCompletionClassInfoTrialList();
                  this.classInfoFormDialogVisible = false;
                  this.submitClassInfoFormLoading = false;
                })
                .catch(() => {
                  this.submitClassInfoFormLoading = false;
                });
            }
          }
        });
      },
      // 关闭试课单弹窗
      classInfoFormClose() {
        this.classInfoFormDialogVisible = false;
      }
    }
  };
</script>

<style></style>
