<template>
  <div class="app-container2">
    <div data-label="页面">
      <div data-label="学员打印--单词">
        <input type="hidden" id="WordPrintCode" value="P213337" />
        <div class="buttom1 clearfix">
          <div class="fl clearfix">
            <a style="background: #3dab93" @click="printEnglish()">只打印英文</a>
            <a style="background: #3dab93; margin-left: 20px" @click="printChinese()">只打印中文</a>
            <a style="background: #3dab93; margin-left: 20px" @click="printAll()">全打印</a>
          </div>
          <div class="buttom2 clearfix">
            <a style="background: #3dab93" @click="exportList()">下载</a>
            <a style="background: #3dab93; margin-left: 20px" @click="printBtn()">打印</a>
            <a style="background: #f0ad4e; margin-left: 20px" @click="goback()">返回</a>
          </div>
        </div>
        <!-- 打印主体  -->
        <div ref="print">
          <div id="print" class="clearfix">
            <div data-label="打印操作" class="name2" style="text-align: center">
              <div class="first-top clearfix" style="padding-top: 30px">
                <span style="font-size: 20px">
                  姓名：
                  <label style="width: 100px; font-weight: normal">{{ printData.name }}</label>
                </span>
                <span style="font-size: 20px" v-if="dayin">
                  教练：
                  <input type="text" style="width: 9vw; font-weight: normal; border: 0px" v-model="a" />
                </span>
                <!-- 教练打印 -->
                <span style="font-size: 20px" v-if="!dayin">
                  教练：
                  <label type="text" style="width: 9vw; font-weight: normal; border: 0px">{{ a }}</label>
                </span>
                <span style="font-size: 20px" v-if="dayin">
                  日期：
                  <input type="text" style="width: 9vw; font-weight: normal; border: 0px" v-model="b" />
                </span>
                <!-- 日期打印 -->
                <span style="font-size: 20px; width: 15%" v-if="!dayin">
                  日期：
                  <label type="text" style="width: 9vw; font-weight: normal; border: 0px">{{ b }}</label>
                </span>
                <span style="font-size: 20px" v-if="dayin">
                  成绩：
                  <input type="text" style="width: 9vw; font-weight: normal; border: 0px" v-model="c" />
                </span>
                <!-- 成绩打印 -->
                <span style="font-size: 20px; width: 15%" v-if="!dayin">
                  成绩：
                  <label type="text" style="width: 9vw; font-weight: normal; border: 0px">{{ c }}</label>
                </span>
                <span style="font-size: 20px; white-space: nowrap; display: inline-block; max-width: 100%">注释:左上角标记点的为复习单词</span>
              </div>
              <hr />
              <div id="prizh" v-if="result.length != 0" style="width: 100%; margin: 0 auto; margin-top: 20px">
                <div style="margin-left: 5px; margin-bottom: 5px" :style="getStyle(itemddd)" v-for="(itemddd, index) in result" :key="index">
                  <div>
                    <table cellspacing="0" cellpadding="0" style="border-bottom: 1px solid #000000; text-align: center">
                      <tr v-for="(trItem, trIndex) in itemddd.data.data" :key="trIndex">
                        <td
                          v-if="English"
                          style="border: 1px solid #000000; border-bottom: 0px; height: 70px; position: relative"
                          :style="{ fontSize: trItem.font + 'px', width: itemddd.data.width + 'px' }"
                        >
                          <div class="circle" v-if="trItem.English.includes('一')">·</div>
                          {{ trItem.English.replace(/一/g, '') }}
                        </td>
                        <td
                          v-if="!English"
                          style="border: 1px solid #000000; width: 160px; border-bottom: 0px; height: 70px"
                          :style="{ fontSize: trItem.font + 'px', width: itemddd.data.width + 'px' }"
                        ></td>
                        <td
                          v-if="Chinese"
                          style="border: 1px solid #000000; border-left: 0px; border-bottom: 0px; width: 160px; height: 70px"
                          :style="{ fontSize: trItem.chFont + 'px', width: itemddd.data.cnWidth + 'px' }"
                        >
                          {{ trItem.Chinese }}
                        </td>
                        <td
                          v-if="!Chinese"
                          style="border: 1px solid #000000; width: 160px; border-bottom: 0px; border-left: 0px; height: 70px"
                          :style="{ fontSize: trItem.chFont + 'px', width: itemddd.data.cnWidth + 'px' }"
                        >
                          &nbsp;
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div style="width: 998px; text-align: right; margin: 0 auto; margin-top: 20px; font-size: 18px; clear: both">
              <p id="time">打印时间：{{ time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import printApi from '@/api/printApi/studentTestPrint';
  import printCloseApi from '@/api/student/studentPrintClose';
  import ls from '@/api/sessionStorage';
  export default {
    data() {
      return {
        // 打印数据
        printData: [],
        dataQuary: {
          wordPrintCode: '',
          name: '',
          courseContentType: ''
        },
        time: '',
        English: true,
        Chinese: true,
        exportLoading: false,
        result: [],
        num: '',
        a: '',
        b: '',
        c: '',
        dayin: true
      };
    },
    created() {
      this.dataQuary.wordPrintCode = ls.getItem('wordPrintCode');
      this.dataQuary.name = ls.getItem('name');
      this.dataQuary.courseContentType = ls.getItem('courseContentType');
      this.dataQuary.studentCode = ls.getItem('studentCode');
      this.dataQuary.merchantCode = ls.getItem('merchantCode');
      this.fetchData(this.dataQuary);
    },
    watch: {
      getItem() {
        if (document.execCommand('print')) {
          console.log('123');
        }
      }
    },
    methods: {
      getStyle(item) {
        if (item.newline) {
          return 'clear: left;float:left';
        } else {
          return 'float:left';
        }
      },

      // 返回
      goback() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1); //返回上一层
      },
      // 显示学员测试结果
      fetchData(dataQuary) {
        const that = this;
        printApi.studentList(dataQuary).then((res) => {
          that.printData = res.data;
          that.splitTable(JSON.parse(that.printData.wordsMap));
        });
      },
      // 分割表格数据,每5个为一组
      splitTable(data) {
        // 格式化后台返回的集合
        let results = [];
        let map = new Map();
        //一个字符占用的PX值
        let max = 32;
        let min = 18;
        let linear = [];
        let nowWidth = 160;
        let nowle = 0;
        let bolckLine = 0;
        let cnwid = 160;
        let beforeLine = 0;
        let line = { width: nowWidth, cnWidth: cnwid, data: [] };

        for (const key in data) {
          let kl = key.length;
          var cn = data[key];
          let font = this.getEnglishFontSize(kl);
          let chFont = this.getChineseFontSize(cn.length);
          if (nowWidth / min < kl) {
            let seek = kl / 3;
            let k = seek - seek / 3;

            line.width = (kl - seek - k) * font + 55;
          }
          if (cnwid / min < cn.length) {
            line.cnWidth = cn.length * chFont + 55;
          }
          var a = { Chinese: cn, English: key, font: font, chFont: chFont };
          var myle = line.width + line.cnWidth + 40;
          if (myle > nowle) {
            nowle = myle;
          }
          line.data.push(a);
          if (line.data.length === 5) {
            var bolck = { newline: false, data: {} };

            bolckLine += nowle;
            if (bolckLine > 1140) {
              bolck.newline = true;
              beforeLine = nowle;
              bolckLine = nowle;
            }

            bolck.data = line;
            results.push(bolck);
            nowWidth = 160;
            cnwid = 160;
            nowle = 160;
            line = { width: nowWidth, cnWidth: cnwid, data: [] };
          }
        }
        if (line.data.length > 0) {
          var bolck = { newline: false, data: {} };
          bolckLine += nowle + 60;

          if (bolckLine > 1140) {
            bolck.newline = true;
            beforeLine = nowle;
            bolckLine = nowle;
          }

          bolck.data = line;
          results.push(bolck);
          nowWidth = 160;
          line = { width: nowWidth, cnWidth: cnwid, data: [] };
        }
        this.result = results;
      },

      function(map, keys, result) {
        map['max'] = 100; // 存放每一次拍完序后的，该过程中产生的最大的元素。
        for (var i = 0; i < keys.length; i++) {
          var temp = 1;
          var mapTe = {};
          var te = '';
          for (var k = 0; k < keys.length; k++) {
            // 和上次循环产生的最大值进行比较
            if (keys[k].length >= map['max']) {
              continue;
            }
            if (temp < keys[k].length) {
              temp = keys[k].length;
              mapTe = map[keys[k]];
              te = keys[k];
            }
          }
          if (te.length != 0) {
            result.push({
              English: te,
              Chinese: mapTe
            });
          }
          map['max'] = temp;
        }
      },
      //动态改变表格字体大小
      getEnglishFontSize(ele) {
        var fs = 18;
        if (ele > 8) {
          fs = 24;
        } else {
          fs = 28;
        }

        return fs;
      },

      getChineseFontSize(ele) {
        var fs;
        if (ele > 10) {
          fs = 18;
        } else {
          fs = 20;
        }
        return fs;
      },

      endCallback() {
        alert('12312');
      },
      // 只打印英文
      printEnglish() {
        this.English = true;
        this.Chinese = false;
        this.getCurrentTime();
      },
      // 只打印中文
      printChinese() {
        this.English = false;
        this.Chinese = true;
        this.getCurrentTime();
      },
      // 全打印
      printAll() {
        this.English = true;
        this.Chinese = true;
        this.getCurrentTime();
      },
      // 打印
      printBtn() {
        this.dayin = false;
        // window.print();
        this.getCurrentTime();
        setTimeout(() => {
          this.$print(this.$refs.print);
        }, 500);
        printCloseApi.editEnable(ls.getItem('wordPrintCode')).then((res) => {});
        this.dayinStatus();
      },
      dayinStatus() {
        setTimeout(() => {
          this.dayin = true;
        }, 1500);
      },
      // 获取当前时间
      getCurrentTime() {
        var _this = this;
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate();
        let hh = new Date().getHours();
        let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
        let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
        _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
        // return _this.gettime
        _this.time = _this.gettime;
      },

      // 下载
      exportList() {
        const that = this;
        that.exportLoading = true;
        printApi.printExport(that.dataQuary.wordPrintCode).then((response) => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: '操作失败',
              message: '文件下载失败'
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; // 获取服务器端的文件名
          link.setAttribute('download', '单词表.xls');
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        });
      }
    }
  };
</script>

<style scoped>
  .circle {
    position: absolute;
    top: 0;
    left: 8px;
    height: 4px;
    width: 4px;
    border-radius: 4px;
    color: #c3c2c2;
  }
  .app-container2 {
    padding: 20px;
    color: #676a6c;
  }

  .wordTable tr:last-child {
    border-bottom: 1px;
  }

  .buttom2 {
    width: auto;
    margin: 0px 180px 0px auto;
    float: right;
    color: rgb(255, 255, 255);
    cursor: pointer;
  }

  .buttom2 a {
    display: block;
    float: left;
    color: rgb(255, 255, 255);
    width: 90px;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
  }

  .buttom1 {
    margin-top: 40px;
    color: rgb(255, 255, 255);
    cursor: pointer;
    margin-left: 200px;
  }

  .buttom1 a {
    display: block;
    float: left;
    color: rgb(255, 255, 255);
    width: 90px;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
  }

  .first-top,
  .first-center {
    width: 1000px;
    margin: 40px auto 0px;
  }

  .first-top span {
    display: block;
    float: left;
    width: 20%;
    text-align: left;
  }

  .first-center span {
    display: block;
    float: left;
    width: 25%;
    text-align: center;
    margin-top: -10px !important;
  }

  .first-top input {
    color: #676a6c;
  }

  .clearfix {
    zoom: 1;
  }

  .printonly {
    display: none;
  }

  @media print {
    input,
    .noprint {
      display: none;
    }

    .printonly {
      display: block;
      width: 50%;
    }
  }

  .clearfix::before,
  .clearfix::after {
    content: '';
    line-height: 0;
    display: table;
  }

  .clearfix::after {
    clear: both;
  }

  .crumbs {
    line-height: 62px;
    margin-top: 0px !important;
  }

  .name2 {
    width: 100%;
    margin: 0 auto;
  }

  @media (max-width: 767px) {
    .buttom1 {
      margin-left: 0;
    }

    .buttom2 {
      width: 100%;
      margin: 10px 0;
    }
  }
</style>
