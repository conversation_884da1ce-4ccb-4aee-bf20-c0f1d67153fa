// 新班级列表/派单中 接口
import request from '@/utils/request';

export default {
  // 指派交付中心
  assignCenter(data) {
    return request({
      url: '/deliver/web/oneMore/forceAssign',
      method: 'post',
      data: {
        classId: data.classId,
        deliverMerchant: data.deliverMerchant
      }
    });
  },
  // 指派交付小组
  assignCenterTeam(data) {
    return request({
      url: '/deliver/web/oneMore/changeTeam',
      method: 'post',
      data: {
        classId: data.classId,
        teamId: data.deliverMerchant
      }
    });
  },

  /**
   * -----------正式课
   */

  // 获取班级列表
  getClassList(params) {
    return request({
      url: '/deliver/web/oneMore/getOneMoreClassStudentList',
      method: 'get',
      params: {
        ...params,
        type: 2 //类型1-试课 2-正式课
      }
    });
  },
  // 获取班级上课信息对接表
  getClassCourseListF(id) {
    return request({
      url: '/deliver/web/student/contact/info/getStudentContactInfoDetail',
      method: 'get',
      params: {
        id
      }
    });
  },
  // 获取班级上课信息对接表 /交付
  getClassCourseListFJF(id) {
    return request({
      url: '/deliver/web/student/contact/info/getStudentByDeliverId',
      method: 'get',
      params: {
        id
      }
    });
  },
  /**
   * -----------试课
   */
  // 获取班级列表

  getClassList2(params) {
    return request({
      url: '/deliver/web/oneMore/getOneMoreClassStudentList',
      method: 'get',
      params: {
        ...params,
        type: 1 //类型1-试课 2-正式课
      }
    });
  },
  // 获取班级试课单
  getClassCourseListT(id) {
    return request({
      url: '/deliver/web/experience/detail',
      method: 'get',
      params: {
        id
      }
    });
  },

  // 获取调班学员
  getAdjustStudentList(params) {
    return request({
      url: '/deliver/web/oneMore/getTransferClassStudentList',
      method: 'get',
      params: {
        ...params
      }
    });
  },
  // 获取可调班级列表
  getAdjustClassList(params) {
    return request({
      url: '/deliver/web/oneMore/getTransferClassList',
      method: 'get',
      params: {
        ...params
      }
    });
  },
  // 新班级列表更换班级
  updateTransferClass(data) {
    return request({
      url: '/deliver/web/oneMore/changeStudentClass',
      method: 'post',
      data
    });
  },
  // 新班级列表删除班级
  deleteTransferClass(params) {
    return request({
      url: '/deliver/web/oneMore/deleteNewClass',
      method: 'post',
      params
    });
  }
};
