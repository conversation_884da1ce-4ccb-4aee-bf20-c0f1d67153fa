/**
 * “接单管理”相关接口
 */
import request from '@/utils/request'

/**
 * 试课新学员---列表查询
 * @param data
 */
export const getTestStudentList = (data) => {
  return request({
    url: '/deliver/web/experience/getExperienceNewStudentList',
    method: 'GET',
    params: data
  })
}
/**
 * 试课新学员---指派
 * @param data
 */
export const assignTestStudent = (data) => {
  return request({
    url: `/deliver/web/experience/newStudentAssign`,
    method: 'PUT',
    params: data
  })
}
/**
 * 试课新学员---拒绝
 * @param data
 */
export const rejectTsetStudent = (id) => {
  return request({
    url: `/deliver/web/experience/reject?id=${id}`,
    method: 'PUT',
  })
}
/**
 * 试课新学员---获取流转历史
 * @param data
 */
export const getHistory = (id) => {
  return request({
    url: `/deliver/web/experience/getExperienceHistory?id=${id}`,
    method: 'GET',
  })
}

/**
 * 正式课新学员---列表
 * @param data
 */
export const getFormalStudentList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getNewStudentList',
    method: 'GET',
    params: data
  })
}
/**
 * 正式课新学员---指派
 * @param data
 */
export const assignFormalStudent = (data) => {
  return request({
    url: '/deliver/web/learnManager/assign',
    method: 'put',
    params: data
  })
}
/**
 * 试课新学员---拒绝
 * @param data
 */
export const rejectFormalStudent = (id) => {
  return request({
    url: `/deliver/web/learnManager/reject?id=${id}`,
    method: 'PUT',
  })
}
/**
 * 正式课新学员---获取流转历史
 * @param data
 */
export const getFormalHistory = (id) => {
  return request({
    url: `/deliver/web/learnManager/getDeliverHistory?id=${id}`,
    method: 'GET',
  })
}

/**
 * 课程类型列表查询
 * @param data
 */
export const getCourseCateList = (data) => {
  return request({
    url: '/znyy/curriculum/list/page',
    method: 'GET',
    params: data
  })
}

/**
 * 获取交付小组成员列表
 * @param data
 */
export const getTeacherList = (data) => {
  return request({
    url: '/deliver/web/teacher/pageTeacherList',
    method: 'POST',
    params: data
  })
}
/**
 * 获取所有交付小组成员列表
 * @param data
 */
export const getAllTeacherList = (data) => {
  return request({
    url: '/deliver/web/teacher/findTeamMember',
    method: 'GET',
    params: data
  })
}
/**
 * 交付中心承单量配置-查询当前交付中心配置
 * @param data
 */
export const getCurrentConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getCurrent',
    method: 'GET',
    params: data
  })
}
/**
 * 交付中心承单量配置-查询交付中心配置详情
 * @param data
 */
export const getByConfigId = (id) => {
  return request({
    url: `/deliver/web/deliverWithstandConfig/getByConfigId?configId=${id}`,
    method: 'GET',
    // params: data
  })
}
/**
 * 交付中心承单量配置-管理员审核
 * @param data
 */
export const auditConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/audit',
    method: 'POST',
    data
  })
}
/**
 * 交付中心承单量配置-管理员查看列表
 * @param data
 */
export const getList = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getList',
    method: 'GET',
    params: data
  })
}
/**
 * 交付中心承单量配置-管理员审核列表
 * @param data
 */
export const auditConfigList = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/adminList',
    method: 'GET',
    params: data
  })
}


/**
 * 交付中心承单量配置-交付中心配置保存接口
 * @param data
 */
export const saveConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/save',
    method: 'POST',
    data: data
  })
}
/**
 * 交付中心承单量配置-交付中心配置撤销接口
 * @param data
 */
export const revokeConfig = (id) => {
  return request({
    url: `/deliver/web/deliverWithstandConfig/revoke?id=${id}`,
    method: 'PUT',

  })
}



/**
 * 交付中心承单量配置-轮排配置
 * @param data
 */
export const getDurationConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getDeliverDurationConfig',
    method: 'GET',
  })
}
/**
 * 交付中心承单量配置-轮排配置
 * @param data
 */
export const getDurationConfigTimes = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getTransferTime',
    method: 'GET',
  })
}
/**
 * 交付中心承单量配置-保存轮排配置
 * @param data
 */
export const saveDurationConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/saveDeliverDurationConfig',
    method: 'POST',
    data
  })
}
/**
 * 交付中心承单量配置-保存轮排配置
 * @param data
 */
export const saveRatio = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/saveRatio',
    method: 'POST',
    data
  })
}

/**
 * 交付中心交付小组配置-获取交付小组列表
 * @param data
 */
export const getTeamList = (data) => {
  return request({
    url: '/deliver/web/team/list/page',
    method: 'GET',
    params: data
  })
}
/**
 * 交付中心交付小组配置-获取交付小组列表
 * @param data
 */
export const getTeamDetail = (id) => {
  return request({
    url: `/deliver/web/team/findTeamById?id=${id}`,
    method: 'GET'
  })
}

/**
 * 交付中心交付小组配置-新增交付小组列表
 * @param data
 */
export const addTeam = (data) => {
  return request({
    url: '/deliver/web/team/save',
    method: 'POST',
    data
  })
}

/**
 * 交付中心交付小组配置-编辑交付小组列表
 * @param data
 */
export const editTeam = (data) => {
  return request({
    url: '/deliver/web/team/updateTeam',
    method: 'POST',
    data
  })
}


/**
 * 交付中心交付小组配置-更换交付小组审核
 * @param data
 */
export const changeTeamAutio = (data) => {
  return request({
    url: '/deliver/web/team/updateTeam',
    method: 'POST',
    data
  })
}
/**
 * 交付中心交付小组配置-更换交付小组申请
 * @param data
 */
export const changeTeam = (data) => {
  return request({
    url: '/deliver/web/team/changeTeam',
    method: 'POST',
    data
  })
}


/**
 * 交付中心交付小组配置-更换交付小组审核列表
 * @param data
 */
export const changeTeamList = (data) => {
  return request({
    url: '/deliver/web/team/findChangeList',
    method: 'GET',
    params: data
  })
}

/**
 * 交付中心交付小组配置-更换交付小组审核列表
 * @param data
 */
export const getHistoryNum = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getSixMonthNum',
    method: 'GET',
    params: data
  })
}

/**
 * 交付中心交付小组配置-更换交付小组审核列表
 * @param data
 */
export const getNeedTime = (data) => {
  return request({
    url: '/deliver/web/experience/needContactInfo',
    method: 'POST',
    data

  })
}

/**
 * 流失单列表
 * @param data
 */
export const getLossTable = (data) => {
  return request({
    url: '/deliver/web/experience/dispatchHistory',
    method: 'GET',
    params: data

  })
}
/**
 * 交付中心指派-获取交付小组列表
 * @param data
 */

export const selectTeam = (id) => {
  return request({
    url: `/deliver/web/team/selectTeam?deliverMerchant=${id}`,
    method: 'GET',
  })
}
/**
 * 交付中心指派-更换交付小组
 * @param data
 */
export const changeTeam2 = (params) => {
  return request({
    url: `/deliver/web/team/changeTeam`,
    method: 'PUT',
    params,
  })
}
export const selectMerchant = (params) => {
  return request({
    url: '/znyy/school/selectMerchant',
    method: 'GET',
    params
  })
}

export const changeBindTeam = (data) => {
  return request({
    url: `/deliver/web/merchant/changeBindTeam`,
    method: 'POST',
    data,
  })
}
export const delTeam = (data) => {
  return request({
    url: `/deliver/web/team/deleteTeam`,
    method: 'PUT',
    params: data,
  })
}
export const getLeaderTeamList = (data) => {
  return request({
    url: `/deliver/web/team/getLeaderTeam`,
    method: 'GET',
    params: data,
  })
}
export const editExp = (data) => {
  return request({
    url: `/deliver/web/experience/updateExperience`,
    method: 'POST',
    data,
  })
}

/**
 * 交付中心交付小组配置-获取简单交付小组列表
 * @param data
 */
export const findTeamList = (data) => {
  return request({
    url: '/deliver/web/team/list/findSimpleTeam',
    method: 'GET',
    params: data
  })
}