<template>
  <div>
    <el-dialog title="请假" width="500px" :visible.sync="dialogVisible" destroy-on-close append-to-body :before-close="closeVisible">
      <el-form v-loading="loading" ref="holidayFormData" :model="holidayFormData" label-position="left" :rules="rules" label-width="120px">
        <!-- 课程信息展示 -->
        <el-form-item label="课程类型：">
          <span class="info-text">{{ infoData.courseType || '--' }}</span>
        </el-form-item>
        <el-form-item label="班级名称：">
          <span class="info-text">{{ infoData.className || '--' }}</span>
        </el-form-item>
        <el-form-item label="学员名称：">
          <span class="info-text">{{ infoData.realName || '--' }}</span>
        </el-form-item>
        <el-form-item label="剩余请假次数：">
          <span class="info-text">{{ infoData.surplusLeaveNum || 0 }}</span>
        </el-form-item>
        <!-- 请假时间选择 -->
        <el-form-item label="请假课程时间：" prop="planStudyId">
          <el-select v-model="holidayFormData.planStudyId" class="full-width" placeholder="请假课程时间" clearable>
            <el-option v-for="item in planStudyList" :key="item.id" :label="item.planStudyTime" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="courseType !== '1'" label="期程补课时间：" prop="makeUpClassTime">
          <!--          isExperience?1:2  实际就是 类型1-试课 2-正式课-->
          <BaseClassStudyTimeSelect
            :isExperience="false"
            v-model="holidayFormData.makeUpClassTime"
            :curriculumId="infoData.curriculumId"
            :grade="infoData.grade"
            placeholder="请选择补课时间"
            @change="handleClassTimeConfigIdChange"
          ></BaseClassStudyTimeSelect>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeVisible">取消</el-button>
        <el-button type="primary" :loading="loading" :disabled="loading" @click="onSubmit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import { studentLeavePlanInfo, studentLeave } from '@/api/oneToManyClass/studySchedule';
  import BaseClassStudyTimeSelect from '@/views/oneToManyClass/components/pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';

  export default {
    name: 'HolidayDialog',
    components: { BaseClassStudyTimeSelect },
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      holidayRow: {
        // 当前行数据
        type: Object,
        default: () => ({})
      },
      courseType: {
        //1试课 2正课
        type: String,
        default: ''
      }
    },
    data() {
      return {
        holidayFormData: {
          grade: '',
          phone: '',
          courseId: null,
          planStudyId: null, //请假课程id
          makeUpClassTime: null // 补课时间
        },
        planStudyList: [], // 请假课程数据源
        infoData: {
          courseType: '', // 课程类型
          className: '', // 班级名称
          realName: '', // 学员名称
          surplusLeaveNum: 0, // 请假次数
          grade: '', //年级
          curriculumId: null // 课程大类id
        },
        loading: false,
        rules: {
          planStudyId: [{ required: true, message: '请选择请假课程时间', trigger: 'change' }]
        }
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      dialogVisible: {
        handler(val) {
          if (val) {
            const { realName, name, courseType, classId, studentCode, phone, className } = this.holidayRow;
            this.getInfo(classId, studentCode);
            this.infoData.realName = this.courseType === '1' ? realName : name;
            this.infoData.courseType = courseType;
            this.infoData.className = className;
            this.holidayFormData.phone = phone;
          }
        },
        immediate: true
      }
    },
    created() {},
    mounted() {},
    methods: {
      async getInfo(classId, studentCode) {
        this.loading = true;
        try {
          const res = await studentLeavePlanInfo({ classId, studentCode });
          const { planStudyList, surplusLeaveNum, courseTime, grade, curriculumId } = res.data;
          this.planStudyList = planStudyList || [];
          this.infoData.surplusLeaveNum = surplusLeaveNum;
          this.infoData.grade = this.holidayFormData.grade = String(grade);
          this.infoData.curriculumId = curriculumId;
          this.holidayFormData.makeUpClassTime = courseTime;
        } catch (e) {
          console.log(e);
          this.closeVisible();
        } finally {
          this.loading = false;
        }
      },
      // 处理上课时间配置id改变事件
      handleClassTimeConfigIdChange(obj) {
        console.log(obj, 'handleClassTimeConfigIdChange');
        const weekMap = { 0: '日', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '日' };
        if (Array.isArray(obj) && obj.length > 0) {
          this.holidayFormData.makeUpClassTime =
            obj
              .sort((a, b) => a.usableWeek - b.usableWeek)
              .map((item) => `周${weekMap[item.usableWeek]}`)
              .join('、') + `${obj[0].startTime.trim()}-${obj[0].endTime.trim()}`;
        }
        console.log(this.holidayFormData, '入参');
      },
      closeVisible() {
        this.holidayFormData = {
          makeUpClassTime: null,
          planStudyId: null
        };
        this.dialogVisible = false;
      },
      onSubmit() {
        this.$refs.holidayFormData.validate((valid) => {
          if (valid) {
            this.submitForm();
          } else {
            return false;
          }
        });
      },

      async submitForm() {
        try {
          const submitData = { ...this.holidayFormData };
          if (this.courseType === '1') {
            submitData.makeUpClassTime = null;
          }
          const res = await studentLeave(submitData);
          this.$message.success('请假申请提交成功');
          this.$emit('submit');
          this.closeVisible();
          this.$refs.holidayFormData.resetFields();
        } catch (e) {
          console.log(e);
        }
      }
    }
  };
</script>
<style scoped lang="scss">
  .info-text {
    display: inline-block;
    padding: 0 15px;
    color: #666;
  }
  .action-buttons {
    text-align: center;
    margin-top: 30px;
  }
  .el-form-item {
    margin-bottom: 18px;
  }
</style>
