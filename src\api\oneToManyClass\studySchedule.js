// 一对多-学习课程表相关接口
import request from '@/utils/request';
/**
 * 获取学习课程表分页数据
 * @param {*} data
 * @returns
 */
export const getStudyScheduleData = (data) => {
  return request({
    url: '/deliver/web/oneMore/getOneMorePlanCourseList',
    method: 'GET',
    params: data
  });
};

/**
 * 获取反馈详情
 * @param {*} data
 * @returns
 */
export const getFeedbackInfo = (data) => {
  return request({
    url: '/deliver/web/oneMore/getCourseStudyInfo',
    method: 'GET',
    params: data
  });
};

// 学考通一对多详情
export const getXktFeedbackInfo = (data) => {
  return request({
    url: '/dyf/web/xkt/feedback/detail',
    method: 'GET',
    params: data
  });
};

/**
 * 获取数学反馈详情
 * @param {*} data
 * @returns
 */
export const getMathFeedbackInfo = (data) => {
  return request({
    url: '/dsx/math/wap/feedback/detail',
    method: 'GET',
    params: data
  });
};

/**
 * 获取数学反馈详情
 * @param {*} data
 * @returns
 */
export const getMathFeedbackInfoExp = (data) => {
  return request({
    url: '/dsx/math/wap/feedback/experience/detail',
    method: 'GET',
    params: data
  });
};
/**
 * 学习课程表删除
 * @param {*} data
 * @returns
 */
export const setStudyScheduleDelete = (classStudyId, courseType) => {
  return request({
    url: '/deliver/web/oneMore/deletePlanStudy',
    method: 'POST',
    data: { classStudyId, courseType }
  });
};
/**
 * 请假记录列表
 * @param {*} data
 * @returns
 */
export const getStudentLeaveList = (data) => {
  return request({
    url: '/deliver/class/studentLeave/getStudentLeaveList',
    method: 'GET',
    params: data
  });
};
/**
 * 请假页面回显接口
 * @param {*} data
 * @returns
 */
export const studentLeavePlanInfo = (data) => {
  return request({
    url: '/deliver/class/studentLeave/studentLeavePlanInfo',
    method: 'GET',
    params: data
  });
};
/**
 * 请假
 * @param {*} data
 * @returns
 */
export const studentLeave = (data) => {
  return request({
    url: '/deliver/class/studentLeave/leave',
    method: 'POST',
    data
  });
};
