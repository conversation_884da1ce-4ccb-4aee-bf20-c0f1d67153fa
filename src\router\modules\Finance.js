import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const FinanceRouter = {
  path: '/Finance/index',
  component: Layout,
  name: 'Finance',
  meta: {
    perm: 'm:use:Finance',
    title: '财务管理',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: '/Finance/personFinan/index',
      component: _import('Finance/personFinan/index'),
      name: 'personFinan',
      meta: {
        perm: 'm:Finance:personFinan',
        title: '个人流水',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: '/refundReview/index',
      component: _import('refundReview/index'),
      name: 'refundReview',
      meta: {
        perm: 'm:refundReview:index',
        title: '甄选退款审核',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/Finance/deliverHours/index',
      component: _import('Finance/deliverHours/index'),
      name: 'deliverHours',
      meta: {
        perm: 'm:Finance:deliverHours',
        title: '集中交付清单',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: '/Finance/deliverHours/studentDeliver',
      component: _import('Finance/deliverHours/studentDeliver'),
      name: 'studentDeliver',
      meta: {
        perm: 'm:Finance:studentDeliver',
        title: '学员集中交付流水',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: '/Finance/assistantWages/index',
      component: _import('Finance/assistantWages/index'),
      name: 'assistantWages',
      meta: {
        perm: 'm:Finance:assistantWages',
        title: '工资管理',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: '/Finance/trialclassDividend/index',
      component: _import('Finance/trialclassDividend/index'),
      name: 'trialclassDividend',
      meta: {
        perm: 'm:Finance:trialclassDividend',
        title: '分润设置',
        icon: 'divisionList',
        noCache: false
      }
    },
    {
      path: '/Finance/coachTurnover/index',
      component: _import('Finance/coachTurnover/index'),
      name: 'coachTurnover',
      meta: {
        perm: 'm:Finance:coachTurnover',
        title: '教练工资流水',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: '/Finance/coachSalaryDetails/index',
      component: _import('Finance/coachSalaryDetails/index'),
      name: 'coachSalaryDetails',
      meta: {
        perm: 'm:Finance:coachSalaryDetails',
        title: '教练工资明细',
        icon: 'authorization_code',
        noCache: false
      }
    }
  ]
};

export default FinanceRouter;
