import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const deliveryConfigurationRouter = {
  path: '/deliveryConfiguration/leaveConfig',
  component: Layout,
  name: 'deliveryConfiguration',
  meta: {
    perm: 'm:deliveryConfiguration:index',
    title: '系统配置',
    icon: 'divisionList',
    noCache: false
  },
  alwaysShow: true,
  children: [
    {
      path: 'leaveConfig_index',
      component: _import('deliveryConfiguration/leaveConfig'),
      name: 'leaveConfig',
      meta: {
        perm: 'm:deliveryConfiguration:leaveConfig_index',
        title: '请假次数配置',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: 'durationConfig_index',
      component: _import('deliveryConfiguration/durationConfig'),
      name: 'durationConfig',
      meta: {
        perm: 'm:deliveryConfiguration:durationConfig_index',
        title: '时长配置',
        icon: 'divisionList',
        noCache: true
      }
    }
  ]
};

export default deliveryConfigurationRouter;
