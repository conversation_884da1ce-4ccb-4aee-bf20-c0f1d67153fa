<template>
  <el-dialog title="编辑" :visible.sync="StudentUpdateFn" width="500px" :close-on-click-modal="false">
    <div v-loading="loading">
      <el-form :model="studentInfo">
        <el-form-item label="学生姓名" label-width="70px">
          <el-input v-model="studentInfo.realName" autocomplete="off" maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="年级" label-width="70px">
          <el-select v-model="grade" placeholder="" style="width: 100%">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学校" label-width="70px">
          <el-input v-model="studentInfo.school" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="StudentUpdateFn = false">取 消</el-button>
        <el-button type="primary" @click="updateStudentInfo">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import { updateStudent, selectStudent } from '@/api/paikeManage/LearnManager';
  export default {
    data() {
      return {
        StudentUpdateFn: false,
        loading: false,
        grade: '',
        studentInfo: {
          realName: '',
          school: '',
          studentCode: ''
        },
        classId: '',
        options: [
          {
            value: '18',
            label: '幼儿园'
          },
          {
            value: '1',
            label: '一年级'
          },
          {
            value: '2',
            label: '二年级'
          },
          {
            value: '3',
            label: '三年级'
          },
          {
            value: '4',
            label: '四年级'
          },
          {
            value: '5',
            label: '五年级'
          },
          {
            value: '6',
            label: '六年级'
          },
          {
            value: '7',
            label: '初一'
          },
          {
            value: '8',
            label: '初二'
          },
          {
            value: '9',
            label: '初三'
          },
          {
            value: '10',
            label: '高一'
          },
          {
            value: '11',
            label: '高二'
          },
          {
            value: '12',
            label: '高三'
          },
          {
            value: '13',
            label: '大一'
          },
          {
            value: '14',
            label: '大二'
          },
          {
            value: '15',
            label: '大三'
          },
          {
            value: '16',
            label: '大四'
          },
          {
            value: '17',
            label: '其他'
          }
        ]
      };
    },
    methods: {
      open(row) {
        this.StudentUpdateFn = true;
        this.studentInfo.studentCode = row.studentCode;
        this.classId = row.classId;
        this.getEditInfo(row.studentCode);
      },
      // 获取编辑详情
      async getEditInfo(id) {
        this.loading = true;
        const res = await selectStudent(id);
        this.studentInfo = res.data;
        this.grade = String(this.studentInfo.grade);
        this.loading = false;
      },
      async updateStudentInfo() {
        this.loading = true;
        let paramdata = {
          classId: this.classId,
          studentCode: this.studentInfo.studentCode,
          realName: this.studentInfo.realName,
          grade: this.grade,
          school: this.studentInfo.school
        };
        const result = await updateStudent(paramdata)
          .then((res) => {
            this.$parent.handleEditClassInfo();
            this.StudentUpdateFn = false;
            this.$message.success(res.message);
          })
          .catch((error) => {
            this.$message.error(error.message);
          })
          .finally(() => {
            this.loading = false;
          });
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
</style>
