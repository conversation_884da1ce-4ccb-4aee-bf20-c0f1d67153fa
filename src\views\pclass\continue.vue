<!--交付中心-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" ref="searchNum" :model="searchNum">
        <!-- 1 -->
        <el-row style="display: flex; flex-wrap: wrap; align-items: center">
          <el-col :span="6">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请选择" style="width: 13vw" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" style="width: 13vw" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门店:" prop="merchantCode">
              <el-input style="width: 13vw" v-model="searchNum.merchantCode" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="课程内容:" prop="courseType">
              <el-select v-model="searchNum.courseType" clearable placeholder="请选择" style="width: 13vw">
                <el-option label="鼎英语" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="教练老师:" prop="teacherName">
              <el-input v-model="searchNum.teacherName" clearable placeholder="请输入" style="width: 13vw"
                size="small"></el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable @change="handlechangeDownCompany">
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-search" style="margin-bottom: 22px; margin-left: 10vw" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>

        <!-- <el-row type="flex">
          <el-col :span="6">
            <el-form-item  label="交付中心编号:" prop="deliverMerchant">
              <el-input style="width: 13vw" v-model="searchNum.deliverMerchant" clearable placeholder="请输入"
                size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverName">
              <el-input style="width: 13vw" v-model="searchNum.deliverName" clearable placeholder="请输入"
                size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-search" style="margin-bottom: 22px; margin-left: 10vw" size="mini"
              @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row> -->

        <!-- <el-row style="display: flex; flex-wrap: wrap; align-items: center">
          <el-col :span="18">
            <el-form-item label="上学时间:" prop="">
              <el-date-picker v-model="timeAll" type="datetimerange" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="changeTime">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-search"  size="mini"
              @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row> -->
      </el-form>
    </el-card>

    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="name" label="姓名" width="100" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="120" header-align="center"></el-table-column>
      <el-table-column prop="firstTime" label="首次上课时间" min-width="130" header-align="center"></el-table-column>
      <el-table-column prop="curriculumName" label="课程类型" min-width="130" header-align="center"></el-table-column>
      <el-table-column prop="deliverMerchant" label="交付中心编号" width="120" header-align="center"></el-table-column>
      <el-table-column prop="deliverName" label="交付中心名称" min-width="130" header-align="center"></el-table-column>
      <el-table-column prop="phone" label="联系方式" header-align="center"></el-table-column>
      <el-table-column prop="merchantCode" label="门店账号" min-width="150" header-align="center"></el-table-column>
      <el-table-column prop="merchantRealName" label="门店负责人" header-align="center"></el-table-column>
      <el-table-column prop="merchantPhone" label="门店手机号" header-align="center"></el-table-column>
      <el-table-column prop="learnTubeName" label="所属学管师" header-align="center"></el-table-column>
      <el-table-column prop="leaveCourseHours" label="剩余交付学时" header-align="center"></el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <FeedbackListDialog @LeaveDialog="LeaveDialog" :LeaveViewStyle="LeaveViewStyle" ref="FeedbackListDialog" :direction="direction" @initList="initData" />
    <FeedbackDialog @LeaveyDialog="LeaveyDialog" :LeaveViewStyley="LeaveViewStyley" ref="FeedbackDialog" :direction="direction" />
    <CoursefeedbackDialog ref="Coursefeedback" @reviewDialog="reviewDialog" :reviewStyle="reviewStyle" :direction="direction" />
  </div>
</template>
<script>
  import FeedbackDialog from './components/FeedbackDialog.vue';
  import { getFeedbackInfo } from '@/api/paikeManage/LearnManager';
  import CoursefeedbackDialog from './components/CoursefeedbackDialog.vue';
  import FeedbackListDialog from '../pclass/components/FeedbackListDialog.vue';
  import { getWarningInfo, getWarningList } from '@/api/studentClass/changeList';
  import { bvstatusList } from '@/api/paikeManage/classCard';

  import { getRenewalReminderConfig, UpdateReConfig, stuentRenewalreminderList } from '@/api/zhujiao';
  import ls from '@/api/sessionStorage';

  export default {
    components: {
      FeedbackDialog,
      FeedbackListDialog,
      CoursefeedbackDialog
    },
    data() {
      return {
        reviewStyle: false,
        LeaveViewStyle: false,
        LeaveViewStyley: false,
        direction: 'rtl', //超哪边打开
        // 日期组件
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                // const end = new Date();
                // const start = new Date();
                // picker.$emit('pick', [start, end]);
                const temp = new Date();
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                const temp = new Date();
                temp.setTime(temp.getTime() - 3600 * 1000 * 24);
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '最近七天',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        },
        tableLoading: false,
        timeAll: [],
        luyouclassCard: [],
        total: null,
        warningId: '',
        searchNum: {
          deliverMerchant: '',
          deliverName: '',
          learnTubeTeacherName: '',
          merchantCode: '',
          name: '',
          studentCode: '',
          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        leaveApplication: {
          id: '',
          type: ''
        },
        isAdmin: false,

        courseList: [],
        chooseTime: '' // 筛选时间
      };
    },
    //计算属性
    computed: {
      classCardstyle_: {
        get() {
          return this.LeaveViewStyle;
        },
        //值一改变就会调用set【可以用set方法去改变父组件的值】
        set(v) {
          //   console.log(v, 'v')
          this.$emit('LeaveDialog', v);
        }
      },
      classCardstyley: {
        get() {
          return this.LeaveViewStyley;
        },
        //值一改变就会调用set【可以用set方法去改变父组件的值】
        set(v) {
          //   console.log(v, 'v')
          this.$emit('LeaveyDialog', v);
        }
      }
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      // this.initData();
      this.getbvstatusList();
    },
    methods: {
      handlechangeDownCompany(e) {
        this.searchNum.curriculumId = e;
        this.initData();
      },
      async getbvstatusList() {
        let { data } = await bvstatusList();
        this.courseList = data;
        let arr = data.filter((item) => item.enName == '鼎英语');
        if (arr && arr.length > 0) {
          this.searchNum.curriculumId = arr[0].id;
        } else {
          this.searchNum.curriculumId = data.length > 0 ? data[0].id : '';
        }
        setTimeout(() => {
          this.initData();
        }, 500);
      },
      // 数据查看
      async lookDataFn(row) {
        this.leaveApplication.id = row.studyId;
        this.leaveApplication.type = row.studyType;
        this.$refs.Coursefeedback.reviewTotal.id = row.id;
        this.$refs.Coursefeedback.reviewTotal.planId = row.planId;
        console.log(row.planId);
        let res = await getFeedbackInfo(this.leaveApplication);
        if (res.code == 20000) {
          this.reviewStyle = true;
        }
        this.$refs.Coursefeedback.studyList1 = res.data;
        this.$refs.Coursefeedback.teacher = row.teacher;
        this.$refs.Coursefeedback.studyType = row.studyType;
      },
      // 处理按钮
      async handleFn(row) {
        this.LeaveViewStyle = true;
        this.warningId = row.id;
        let res = await getWarningInfo(this.warningId);
        this.$refs.FeedbackListDialog.warningList = res.data;
        this.$refs.FeedbackListDialog.addwarningList.warningId = res.data.id;
        this.initData();
      },
      // 查看
      async lookFn(row) {
        console.log(row);
        this.LeaveViewStyley = true;
        this.warningId = row.id;
        let res = await getWarningInfo(this.warningId);
        this.$refs.FeedbackDialog.warningList = res.data;
      },
      LeaveDialog(v) {
        this.LeaveViewStyle = v;
      },
      LeaveyDialog(v) {
        this.LeaveViewStyley = v;
      },
      async initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      async initData() {
        // 判断为null的时候赋空
        // if (!this.timeAll) {
        //   this.timeAll = [];
        // }
        this.tableLoading = true;
        // this.searchNum.startTime = this.timeAll[0];
        // this.searchNum.endTime = this.timeAll[1];
        let { data } = await stuentRenewalreminderList(this.searchNum);
        this.tableLoading = false;
        this.luyouclassCard = data.data;
        this.total = Number(data.totalItems);
      },
      // 转文字
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      courseType(val) {
        if (val.courseType == 1) {
          return '鼎英语';
        }
      },
      studyStatus(val) {
        if (val.studyStatus == 0) {
          return '未上课';
        } else if (val.studyStatus == 2) {
          return '已上课';
        }
      },
      status(val) {
        if (val.status == 1) {
          return '待处理';
        } else if (val.status == 2) {
          return '已处理';
        }
      },
      reviewDialog(v) {
        this.reviewStyle = v;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },

      //重置
      rest() {
        this.$refs.searchNum.resetFields();

        this.searchNum.startTime = '';
        this.searchNum.endTime = '';
        this.timeAll = [];
        this.searchNum.curriculumId = this.courseList.length ? this.courseList[0].id : '';
        this.initData();
      },

      changeTime(e) {
        this.searchNum.startTime = this.chooseTime[0];
        this.searchNum.endTime = this.chooseTime[1];
      }
    }
  };
</script>
<style lang="scss" scoped>
  .redClass {
    color: red;
  }

  .GreenClass {
    color: green;
  }
</style>
