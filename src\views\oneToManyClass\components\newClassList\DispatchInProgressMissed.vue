<!-- 一对多-新班级列表-试课派单中 -->
<template>
  <div>
    <el-form :model="searchNum" ref="query" label-width="100px" class="container-card my-form" :inline="true" size="small">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="40">
        <el-col :span="6">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('派单状态') && isAdmin">
          <el-form-item label="派单状态:">
            <el-select v-model="searchNum.dispatchOrderStatus" clearable size="small" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option label="进行中" value="1"></el-option>
              <el-option label="无人接单" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('交付中心编号')">
          <el-form-item label="交付中心编号:" prop="deliverMerchant">
            <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('交付中心名称')">
          <el-form-item label="交付中心名称:" prop="deliverName">
            <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="班级:" prop="classCodeOrName">
            <el-input v-model="searchNum.classCodeOrName" clearable placeholder="请输入班级名称或编号" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="searchNum.grade" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="星期：" prop="dayOfWeek">
            <el-select v-model.trim="searchNum.dayOfWeek" filterable clearable placeholder="请选择">
              <el-option v-for="item in weeklist" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="时间：">
            <el-row class="classTime">
              <el-col :span="5">
                <el-time-select
                  placeholder="开始时间"
                  v-model="searchNum.lessonStartTime"
                  prefix-icon="''"
                  suffix-icon="''"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '24:00'
                  }"
                  style="width: 80px; border: none"
                ></el-time-select>
              </el-col>
              <el-col :span="5" style="text-align: right; margin: 0 10px 0 20px">至</el-col>
              <el-col :span="5">
                <el-time-select
                  placeholder="结束时间"
                  v-model="searchNum.lessonEndTime"
                  prefix-icon="''"
                  suffix-icon="''"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '23:30',
                    minTime: searchNum.lessonStartTime
                  }"
                  style="width: 80px; border: none"
                ></el-time-select>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学员名称:" prop="studentName">
            <el-input v-model="searchNum.studentName" clearable placeholder="请输入学员名称" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-row style="margin: 20px 0 20px 20px">
      <el-col :span="2">
        <el-button type="primary" @click="headerList()">列表显示属性</el-button>
      </el-col>
    </el-row>
    <!--  -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      :header-cell-style="{ background: '#f5f7fa' }"
      :cell-style="{ 'text-align': 'center' }"
      height="400"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :width="getWidth(item.value)"
        :show-overflow-tooltip="['grade'].includes(item.value)"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="showDetail(row)">补课单</el-button>
            <el-button type="primary" size="mini" @click="changeClass(row, '3')">更换班级</el-button>
            <el-button type="danger" size="mini" @click="deleteData(row.id, '3')">删除</el-button>
          </div>
          <!-- 期望上课时间 -->
          <div v-else-if="item.value == 'classTime'">
            <el-row style="white-space: break-spaces">
              {{ row.classTime }}
            </el-row>
          </div>
          <!-- 学员 -->
          <div v-else-if="item.value == 'studentList'">
            <el-tooltip
              v-if="row.studentList"
              :disabled="row.studentList.length < 3"
              class="item"
              effect="dark"
              :content="row.studentList.map((item) => `${item.studentName}(${item.studentCode})`).join(', ')"
              placement="top"
            >
              <el-row>
                <el-col :span="24" v-for="(row, index) in (row.studentList || []).slice(0, 3)" :key="index">
                  <span>{{ `${row.studentName}(${row.studentCode})` }}</span>
                </el-col>
                {{ row.studentList.length > 3 ? '...' : '' }}
              </el-row>
            </el-tooltip>
            <el-row v-else>-</el-row>
          </div>
          <!-- 年级 -->
          <span v-else-if="item.value == 'grade'">
            {{ row.gradeName || '-' }}
          </span>
          <!-- 派单状态 -->
          <div v-else-if="item.value == 'dispatchOrderStatus'">
            <span>
              {{ row.dispatchOrderStatus == 1 ? '进行中' : row.dispatchOrderStatus == 2 ? '无人接单' : '已接单' }}
            </span>
          </div>
          <!-- 派单时间 -->
          <el-row :gutter="20" type="flex" justify="center" align="middle" v-else-if="item.value == 'dispatchOrderDate'">
            <el-col :span="18" :offset="0" v-if="row[item.value]">
              <div>{{ row[item.value] }}</div>
            </el-col>
            <el-col :span="6" :offset="0" v-if="isAdmin">
              <el-button type="text" @click="getDetail(row.id)">详情</el-button>
            </el-col>
          </el-row>
          <!-- 课程类型 -->
          <div v-else-if="item.value == 'curriculumId'">
            <span>
              {{ getCourse(row.curriculumId) || '-' }}
            </span>
          </div>
          <!-- 剩余接单时间 -->
          <span v-else-if="item.value == 'timeOut'">
            <statistic
              v-if="row.timeOut && getTrueTime(row.timeOut)"
              ref="statistic"
              format="HH:mm:ss"
              :valueClass="statusClass(row.timeOut)"
              :value="getResidueTime(row['timeOut'])"
              :key="'statistic' + row['timeOut']"
              time-indices
            ></statistic>
            <span v-else>{{ '-' }}</span>
          </span>
          <!-- 状态 -->
          <div v-else-if="item.value == 'status'">
            <el-tag v-if="row.status == 1" type="success" effect="dark" size="mini" style="width: 55px">正常</el-tag>
            <el-tag v-else type="danger" effect="dark" size="mini" style="width: 55px">已停用</el-tag>
          </div>
          <!-- 班级人数 -->
          <div v-else-if="item.value == 'classStudentCount'">
            <span style="padding-right: 10px">{{ row.classStudentCount }}</span>
            <el-tag v-if="row.classStudentCount >= row.studentCount" type="danger" effect="dark" size="mini">满</el-tag>
          </div>
          <!-- 其他 -->
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row v-if="tableList && tableList.length > 0" type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
    <!-- 历史派单记录弹窗 -->
    <ClasstHistoryOrderDispatchDialog :visible.sync="historyOrderDispatchDialogVisible" :classId="historyOrderDispatchClassId" />
    <!--    补课单-->
    <MakeUpClassDetailDialog v-model="showDetailDialog" :gradeList="gradeList" :type="type" :detail-info="currentDetail" />
    <!-- 试课-派单中-更换班级 -->
    <ChangeClassDialog ref="ChangeClassDialog" @updateList="initData"></ChangeClassDialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
import { getOneToMoreClassList } from '@/api/oneToManyClass/newClassList';
import checkPermission from '@/utils/permission';
import statistic from '../statistic.vue';
import HeaderSettingsDialog from '../../../pclass/components/HeaderSettingsDialog.vue';
import ClasstHistoryOrderDispatchDialog from '../classList/ClasstHistoryOrderDispatchDialog.vue';
import MakeUpClassDetailDialog from './components/MakeUpClassDetailDialog.vue';
import { getTemporaryClassList } from '@/api/oneToManyClass/WaitingBuildingClass';
import ChangeClassDialog from './components/ChangeClassDialog.vue';
export default {
  name: 'DispatchInProgressMissed',
  components: {
    HeaderSettingsDialog,
    statistic,
    ClasstHistoryOrderDispatchDialog,
    MakeUpClassDetailDialog,
    ChangeClassDialog
  },
  props: {
    gradeList: {
      type: Array,
      default: () => []
    },
    type: {
      //补课-派单中 '5' 补课-等待成班中'6'
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters(['code'])
  },
  data() {
    return {
      screenWidth: window.screen.width, //屏幕宽度
      courseList: [], // 课程类型
      total: 0, // 总数
      searchNum: {
        curriculumId: '', // 课程类型Id
        deliverMerchant: '', // 交付中心编号
        deliverName: '', // 交付中心名称
        dispatchOrderStatus: '', // 派单状态 1 派单中 2 无人接单
        classCodeOrName: '', // 班级名称或编号
        grade: '', //年级
        classCode: '', //班级
        dayOfWeek: '', // 周
        lessonStartTime: '', // 开始时间
        lessonEndTime: '', // 结束时间
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      historyOrderDispatchDialogVisible: false, // 历史派单记录弹框
      historyOrderDispatchClassId: '', // 历史派单记录班级id
      tableLoading: false, // 表格加载状态
      tableList: [], // 表格数据
      headerSettings: [
        { name: '班级名称', value: 'className' },
        { name: '班级编号', value: 'classCode' },
        { name: '期望上课时间', value: 'classTime' },
        { name: '学员', value: 'studentList' },
        { name: '课程类型', value: 'curriculumId' },
        { name: '课程内容', value: 'courseContent' },
        { name: '派单状态', value: 'dispatchOrderStatus' },
        { name: '派单时间', value: 'dispatchOrderDate' },
        { name: '派单交付小组', value: 'deliverGroup' },
        { name: '剩余接单时间', value: 'timeOut' },
        // { name: '年级', value: 'grade' },
        { name: '操作', value: 'operate' }
      ],
      HeaderSettingsStyle: false, // 列表属性弹框
      tableHeaderList: [], // 获取表头数据
      isTeamLeader: false, // 是否是交付中心组长
      isAdmin: false, // 是否是管理员
      currentClassId: '',
      showDetailDialog: false, //补课单弹框
      currentDetail: {},
      weeklist: [
        { value: 1, label: '周一' },
        { value: 2, label: '周二' },
        { value: 3, label: '周三' },
        { value: 4, label: '周四' },
        { value: 5, label: '周五' },
        { value: 6, label: '周六' },
        { value: 7, label: '周日' }
      ]
    };
  },
  created() {
    this.isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';
    this.isAdmin = checkPermission(['admin', 'JiaofuManager']);
    if (this.isAdmin) {
      this.headerSettings.push(
        {
          name: '交付中心名称',
          value: 'deliverName'
        },
        {
          name: '交付中心编号',
          value: 'deliverMerchant'
        }
      );
    }
  },
  mounted() {
    this.getCourseTypeList(); // 获取课程类型列表
    this.getHeaderList(); // 获取表头数据
    this.initData(); // 初始化数据
  },
  methods: {
    checkPermission,
    // 获取课程类型列表
    async getCourseTypeList() {
      try {
        const res = await getOneToMoreClassList({});
        this.courseList = res.data;
      } catch (e) {
        console.log(e, '获取课程类型列表');
      }
    },
    // 重置搜索框
    rest() {
      this.searchNum.lessonStartTime = '';
      this.searchNum.lessonEndTime = '';
      this.searchNum.dispatchOrderStatus = '';
      this.$refs.query.resetFields();
      this.initData01();
    },
    // 搜索
    initData01() {
      let startTimeStr = this.searchNum.lessonStartTime;
      let endTimeStr = this.searchNum.lessonEndTime;
      if (startTimeStr && !endTimeStr) {
        this.$message.error('请选择结束时间');
        return;
      }
      if (!startTimeStr && endTimeStr) {
        this.$message.error('请选择开始时间');
        return;
      }
      if (startTimeStr && endTimeStr) {
        if (startTimeStr >= endTimeStr) {
          this.$message.error('结束时间必须大于开始时间');
          return;
        }
      }
      (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
    },
    async initData() {
      this.tableLoading = true;
      try {
        let { data } = await getTemporaryClassList(this.searchNum);
        if (!data) {
          this.tableList = [];
          this.total = 0;
          return;
        }
        this.total = Number(data.totalItems);
        this.tableList = data.data;
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    // 获取宽度
    getWidth(val) {
      const widthMap = {
        classTime: '300px',
        classCode: '300px',
        studentList: '260px',
        operate: '260px',
        dispatchOrderDate: '200px'
      };
      return widthMap[val] || '180px';
    },

    //打开补课单
    showDetail(row) {
      this.currentDetail = {
        ...row,
        courseName: this.getCourse(row.curriculumId),
        gradeLabel: this.getGrade(row.grade)
      };
      this.showDetailDialog = true;
    },
    // 动态class
    statusClass(time, source) {
      let normTime = 10 * 60 * 1000;
      let newTime = new Date(time).getTime() - Date.now();
      if (newTime <= normTime) {
        return 'error';
      } else {
        return 'normal';
      }
    },
    // 时间转换方法
    getResidueTime(time) {
      if (time) {
        let time1 = new Date(time).getTime();
        return time1;
      } else {
        return 0;
      }
    },
    getTrueTime(time) {
      if (time) {
        let year = new Date(time).getFullYear();
        let newTime = new Date(time).getTime() - Date.now();
        if (year > 6000 || newTime < 0) {
          return false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    },
    // 派单历史
    async getDetail(id) {
      this.historyOrderDispatchClassId = id;
      this.historyOrderDispatchDialogVisible = true;
    },
    // 拿到年级
    getGrade(val) {
      let grade = this.gradeList.find((item) => {
        return item.value == val;
      });

      return grade && grade.label;
    },
    //拿到课程类型
    getCourse(val) {
      let classType = this.courseList.find((item) => {
        return item.id == val;
      });
      return classType && classType.enName;
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    // 获取表头设置
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item?.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    // 接收子组件数据动态控制表头弹窗
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 接收子组件选择的表头数据
    selectedItems(arr) {
      if (arr) {
        let data = {
          type: 'DispatchInProgressMissed',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      }
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then((res) => {
        this.$message.success('操作成功');
        this.HeaderSettingsStyle = false;
        this.getHeaderList();
      });
    },
    // 获取表头设置
    async getHeaderList() {
      let data = {
        type: 'DispatchInProgressMissed'
      };
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          let tableHeaderList = JSON.parse(res.data.value);
          this.tableHeaderList = tableHeaderList.filter((item) => item);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      });
    },
    // 查看表头列表是否有该字段
    hasNoPermissionField(field) {
      let has = this.tableHeaderList.some((i) => {
        if (i.name == field || i.value == field) {
          return true;
        }
        return false;
      });
      return has;
    },
    changeClass(row, type) {
      this.$refs.ChangeClassDialog.open(row, type);
    },
    deleteData(id, type) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        orderApi
          .deleteTransferClass({
            classId: id,
            classType: type
          })
          .then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.initData();
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.classTime {
  border: 1px solid #ccd;
  border-radius: 4px;
  ::v-deep.el-input__inner {
    padding: 0 12px;
    border: none;
  }
  ::v-deep.el-input__suffix {
    top: 1px;
    right: 2px;
  }
}
</style>
