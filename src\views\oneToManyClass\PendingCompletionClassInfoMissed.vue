<!-- 交付中心-一对多学员管理-待完善上课信息表-补课 -->
<template>
  <div>
    <!-- 查询栏 -->
    <el-card class="frame" shadow="never">
      <el-form label-width="120px" ref="searchNum" :model="searchNum">
        <el-row :gutter="30" style="display: flex; flex-wrap: wrap">
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名:" prop="studentName">
              <el-input v-model="searchNum.studentName" class="full-width" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="联系方式:" prop="phone">
              <el-input v-model="searchNum.phone" class="full-width" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" class="full-width" placeholder="请选择" clearable>
                <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="search">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="reset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="tableList" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 200 : ''"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'curriculumName'">
            {{ getCourse(row.curriculumId) }}
          </div>
          <div v-if="item.value == 'leaveCourseTime'">
            {{ formatStudyTimeRange(row.startStudyTime, row.endStudyTime) }}
          </div>
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="clickHandleFillMakeUp(row)">填写补课单</el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />

    <!--填写补课单弹窗 -->
    <fill-make-up-dialog v-model="fillMakeUpVisible" :clickRowData="tableRow" @submit="refreshTable" />
  </div>
</template>

<script>
  import { formatStudyTimeRange } from '@/utils';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import BaseClassStudyTimeSelect from './components/pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';
  import { getStudentLeavePageList } from '@/api/oneToManyClass/pendingCompletionClassInfo';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import FillMakeUpDialog from '@/views/oneToManyClass/components/makeUpClassList/fillMakeUpDialog.vue';
  export default {
    name: 'PendingCompletionClassInfoMissed',
    components: {
      FillMakeUpDialog,
      HeaderSettingsDialog,
      BaseClassStudyTimeSelect
    },
    data() {
      return {
        // 查询栏
        searchNum: {
          studentName: '', // 姓名
          phone: '', // 电话
          curriculumId: '' // 课程类型
        },
        formatStudyTimeRange,
        curriculumList: [], //  课程类型下拉数据

        // 列表属性弹框
        HeaderSettingsStyle: false,
        headerSettings: [
          { name: '姓名', value: 'studentName' },
          { name: '联系方式', value: 'phone' },
          { name: '操作', value: 'operate' },
          { name: '课程类型', value: 'curriculumName' },
          { name: '请假发起时间', value: 'createTime' },
          { name: '请假课程时间', value: 'leaveCourseTime' },
          { name: '门店名称', value: 'merchantName' },
          { name: '门店手机号', value: 'merchantPhone' }
        ],
        tableHeaderList: [],

        // 表格数据
        tableLoading: false,
        tableList: [],

        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        //填写补课单弹框
        fillMakeUpVisible: false,
        tableRow: {} // 当前行数据
      };
    },
    mounted() {
      this.getHeaderList();
      this.getCurriculumList();
      this.getPageList();
    },
    watch: {},

    methods: {
      // 获取课程类型下拉列表
      async getCurriculumList() {
        try {
          const res = await getAllOneToManyCurriculumType();
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        } catch (e) {
          console.log(e);
        }
      },
      //拿到课程类型
      getCourse(val) {
        let classType = this.curriculumList.find((item) => {
          return item.id == val;
        });
        return classType && classType.enName;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        console.log(arr);
        if (arr) {
          let data = {
            type: 'PendingCompletionClassInfoMissed',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderList();
        });
      },
      // 获取表头设置
      async getHeaderList() {
        let data = {
          type: 'PendingCompletionClassInfoMissed'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getPageList();
      },
      // 重置
      reset() {
        this.searchNum = {};
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getPageList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getPageList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getPageList();
      },
      // 获取列表
      getPageList() {
        this.tableLoading = true;
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getStudentLeavePageList(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data || [];
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableList = [];
            this.tableLoading = false;
          });
      },
      // 填写试课单
      clickHandleFillMakeUp(row) {
        this.fillMakeUpVisible = true;
        this.tableRow = row;
      },
      refreshTable() {
        this.getPageList();
      }
    }
  };
</script>

<style></style>
