<!--交付中心-入库管理-人员入库-教练的页面-->
<template>
  <div>
    <el-card v-if="zhuisfor">
      <el-form label-width="100px" class="frame" ref="zhuSou" :model="zhuSou">
        <!-- 1 -->
        <el-row type="flex" justify="space-around">
          <!-- <el-col :span="8">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="zhuSou.name" placeholder="请选择" size="mini" style="width: 250px"></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="8" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                :filter-method="filterValue"
                clearable
                v-model="zhuSou.name"
                size="small"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别:" prop="sex">
              <el-select v-model="zhuSou.sex" placeholder="请选择" size="small" clearable style="width: 250px">
                <el-option label="男" value="1"></el-option>
                <el-option label="女" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="授课方式:" prop="teachingType">
              <el-select v-model="zhuSou.teachingType" placeholder="请选择" size="small" clearable>
                <el-option label="远程" value="1"></el-option>
                <el-option label="线下" value="2"></el-option>
                <el-option label="远程和线下" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 2 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="8">
            <el-form-item label="联系方式:" prop="mobile">
              <el-input v-model="zhuSou.mobile" placeholder="请选择" size="small" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所教学段:" prop="gradeStage">
              <el-select v-model="zhuSou.gradeStage" placeholder="请选择" size="small" clearable style="width: 250px">
                <el-option label="小学" value="1"></el-option>
                <el-option label="初中" value="2"></el-option>
                <el-option label="高中" value="3"></el-option>
                <el-option label="大学" value="4"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="*所教学段:">
             <el-select
               v-model="searchListzz"
               multiple
               clearable
               placeholder="请选择"
             >
               <el-option
                 v-for="(item,index) in options"
                 :key="index"
                 :label="item.label"
                 :value="item.value"
               ></el-option>
             </el-select>
           </el-form-item> -->
          </el-col>
          <el-col :span="8">
            <el-form-item label="所教模块:" prop="teachModule">
              <el-select v-model="zhuSou.teachModule" placeholder="请选择" size="small" clearable>
                <el-option label="单词" value="1"></el-option>
                <el-option label="语法" value="2"></el-option>
                <el-option label="阅读" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 3 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="8">
            <el-form-item label="岗位类型:" prop="postType">
              <el-select v-model="zhuSou.postType" placeholder="请选择" size="small" style="width: 200px" clearable>
                <el-option label="全职" value="1"></el-option>
                <el-option label="兼职" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isAdmin">
            <el-form-item label="交付中心编号:" prop="deliverMerchant">
              <el-input v-model.trim="zhuSou.deliverMerchant" placeholder="请输入" size="small" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属交付小组:" prop="deliverMerchant">
              <!-- <el-input v-model.trim="zhuSou.teamName" placeholder="请输入" size="small" style="width: 250px"></el-input> -->
              <el-select v-model="zhuSou.teamId" clearable size="small" filterable placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item, index) in leaderTeamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="zhuSou.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 4 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="12">
            <el-form-item label="入学年份：">
              <el-date-picker
                v-model="zhuSou.enrollmentYearStart"
                format="yyyy"
                value-format="yyyy"
                type="year"
                placeholder="开始年份"
                clearable
                :picker-options="pickerOptionsStart"
              ></el-date-picker>
              至
              <el-date-picker
                v-model="zhuSou.enrollmentYearEnd"
                format="yyyy"
                value-format="yyyy"
                type="year"
                placeholder="结束年份"
                clearable
                :picker-options="pickerOptionsEnd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新增日期:" prop="createTime">
              <!-- <el-date-picker v-model="zhuSou.createTime" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker> -->
              <el-date-picker
                v-model="value1"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                size="small"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="changeTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="isAdmin ? 8 : 10" style="padding-left: 30px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchBtn">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { searchTeacherApi } from '@/api/rukuManage/zhuTeacher';
import { selAllTeacher, findTeamList } from '@/api/studentClass/changeList';
import { bvstatusList } from '@/api/paikeManage/classCard';
import { getLeaderTeamList } from '@/api/orderManage';
import ls from '@/api/sessionStorage';

export default {
  name: 'zhujiaoTop',
  props: {
    zhuisfor: {
      // 定义接收的类型 还可以定义多种类型 [String, Undefined, Number]
      // 如果required为true,尽量type允许undefined类型，因为传递过来的参数是异步的。或者设置默认值。
      type: Boolean,
      // 定义是否必须传
      required: true
    }
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  data() {
    return {
      // 入学年份 新增日期
      matriculationYear: '',
      addDate: '',
      // 点谁谁true，默认都false
      xueisTo: false,
      zhuSou: {
        name: '',
        sex: '',
        enrollmentYearStart: '',
        enrollmentYearEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        gradeStage: '',
        mobile: '',
        teachModule: '',
        teachingType: '',
        postType: '',
        curriculumId: '',
        pageNum: 1,
        pageSize: 10
      },
      value1: [],
      pickerOptionsStart: {
        disabledDate(time) {
          return false; // 开始年份没有前置条件，所以所有年份都可选
        }
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          // 禁用所有早于开始年份的日期
          return time.getFullYear() < this.zhuSou.enrollmentYearStart;
        }
      },
      searchList: [],
      searchListzz: '',
      teachModulezz: '',
      isAdmin: false,
      option: [],
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        name: ''
      },
      courseList: [],
      leaderTeamList: []
    };
  },
  watch: {
    // 当开始年份变化时，更新结束年份选择器的禁用日期
    'zhuSou.enrollmentYearStart'(newVal) {
      this.pickerOptionsEnd.disabledDate = (time) => {
        return time.getFullYear() < newVal;
      };
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
    console.log(this.isAdmin);
    this.getTeacherList();
  },
  mounted() {
    this.getbvstatusList();
    this.getTeamList();
    this.getTeamList();
  },
  methods: {
    changeTime(e) {
      console.log('🚀 ~ changeTime ~ e:', e);
      if (e == null || e.length === 0) {
        this.zhuSou.createTimeStart = '';
        this.zhuSou.createTimeEnd = '';
        return;
      }
      this.zhuSou.createTimeStart = e[0];
      this.zhuSou.createTimeEnd = e[1];
    },

    async getTeamList() {
      let { data } = await findTeamList();
      // console.log(data);
      // console.log(data.data, '11111111111111111');
      this.leaderTeamList = [{ teamName: '无小组', id: '0' }, ...data.data];
    },
    getbvstatusList() {
      bvstatusList({}).then((res) => {
        this.courseList = res.data;
      });
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    // 获取教练
    async getTeacherList() {
      let allData = await selAllTeacher(this.selectObj);
      this.option = this.option.concat(allData.data.data);
    },

    filterValue(value) {
      console.log(value);
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = value;
      this.getTeacherList();
    },

    changeMessage() {
      this.$forceUpdate();
    },

    clearSearchRecord() {
      setTimeout(() => {
        if (this.zhuSou.name == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      }, 500);
      this.$forceUpdate();
    },
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
      }
    },
    async searchBtn() {
      // 验证入学年份的开始和结束时间
      if (this.zhuSou.enrollmentYearStart && !this.zhuSou.enrollmentYearEnd) {
        this.$message.warning('请选择结束年份');
        return;
      }

      if (!this.zhuSou.enrollmentYearStart && this.zhuSou.enrollmentYearEnd) {
        this.$message.warning('请选择开始年份');
        return;
      }

      if (this.zhuSou.enrollmentYearStart && this.zhuSou.enrollmentYearEnd) {
        // 注意：如果是字符串类型的年份，需要先转为数字再比较
        const start = Number(this.zhuSou.enrollmentYearStart);
        const end = Number(this.zhuSou.enrollmentYearEnd);

        if (start > end) {
          this.$message.warning('开始年份不能大于结束年份');
          return;
        }
      }
      // console.log("---------------2222")
      // this.zhuSou.teachModule = this.teachModulezz.toString()
      // this.zhuSou.gradeStage=this.searchListzz.toString();
      this.zhuSou.pageNum = 1;
      // let { data } = await searchTeacherApi(this.zhuSou)
      // this.searchList = data;
      // this.$emit('Searchlist', this.searchList)
      this.$emit('zhuSou', this.zhuSou);
    },

    // 切换分页
    async initData() {
      console.log('---------------3333');
      // this.zhuSou.teachModule = this.teachModulezz.toString()
      // this.zhuSou.gradeStage=this.searchListzz.toString();
      let { data } = await searchTeacherApi(this.zhuSou);
      this.searchList = data;
      this.$emit('Searchlist', this.searchList);
      this.$emit('zhuSou', this.zhuSou);
    },
    //重置
    rest() {
      this.$refs.zhuSou.resetFields();
      this.addDate = '';
      this.matriculationYear = '';
      this.zhuSou = {
        name: '',
        sex: '',
        gradeStage: '',
        mobile: '',
        teachModule: '',
        teachingType: '',
        postType: '',
        curriculumId: '',
        createTimeStart: '',
        createTimeEnd: '',
        pageNum: 1,
        pageSize: 10
      };
      this.value1 = null;
      this.$nextTick(() => {
        this.value1 = [];
      });
      this.searchBtn();
    }
  }
};
</script>

<style>
</style>
