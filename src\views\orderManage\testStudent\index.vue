<!--交付中心-接单管理-试课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame" v-if="isAdmin">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="5">
            <el-form-item label="姓名：" label-width="120px" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5">
            <el-form-item label="门店:" label-width="70px" prop="deliverMerchantName">
              <el-input v-model.trim="querydata.deliverMerchantName" size="small"
                placeholder="请输入门店账号或门店手机号"></el-input>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="5" >
            <el-form-item label="派单来源:" prop="deliverSource">
              <el-select v-model="querydata.deliverSource" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item,index) in deliverSources" :key="index" :label="item.name"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="4">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="querydata.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交付中心编号:" label-width="200px" prop="deliverCode">
              <el-input v-model.trim="querydata.deliverCode" @change="changeInput" size="small" placeholder=""></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付中心名称：" label-width="120px" prop="deliverName">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                remote
                clearable
                v-model="querydata.deliverName"
                filterable
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="(item, index) in option" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="派单状态：" prop="dispatchOrderStatus">
              <el-select v-model="querydata.dispatchOrderStatus" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="进行中" value="1"></el-option>
                <el-option label="无人接单" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="学生来源：" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="系统派单" value="1"></el-option>
                <el-option label="门店派单" value="2"></el-option>
                <el-option label="总部派单" value="3"></el-option>
                <el-option label="变更交付小组" value="4"></el-option>
                <el-option label="主交付中心派单" value="7"></el-option>
                <el-option label="次交付中心派单" value="8"></el-option>
                <el-option label="轮排派单" value="9"></el-option>
                <el-option label="管理员处理" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 交付小组组长头部 -->
    <div class="frame" v-else-if="isTeamLeader">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim.number="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付小组:" prop="team">
              <el-select v-model="querydata.teamId" clearable size="small" placeholder="请选择" style="width: 10vw" @change="handlechangeDownCompany">
                <el-option v-for="(item, index) in leaderTeamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生来源:" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="系统派单" value="1"></el-option>
                <el-option label="门店派单" value="2"></el-option>
                <el-option label="总部派单" value="3"></el-option>
                <el-option label="变更交付小组" value="4"></el-option>
                <el-option label="主交付中心派单" value="7"></el-option>
                <el-option label="次交付中心派单" value="8"></el-option>
                <el-option label="轮排派单" value="9"></el-option>
                <el-option label="管理员处理" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 交付中心头部 -->
    <div class="frame" v-else>
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim.number="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5" :xs="24">
            <el-form-item label="派单来源:" prop="deliverSource">
              <el-select v-model="querydata.deliverSource" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item,index) in deliverSources" :key="index" :label="item.name"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="5" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="querydata.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生来源:" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="系统派单" value="1"></el-option>
                <el-option label="门店派单" value="2"></el-option>
                <el-option label="总部派单" value="3"></el-option>
                <el-option label="变更交付小组" value="4"></el-option>
                <el-option label="主交付中心派单" value="7"></el-option>
                <el-option label="次交付中心派单" value="8"></el-option>
                <el-option label="轮排派单" value="9"></el-option>
                <el-option label="管理员处理" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 列表显示属性 -->
    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <!-- 表格 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      id="out-table"
      v-loading="tableLoading"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
      size="mini"
      fit
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="index"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        min-width="150"
        :width="item.value == 'operate' ? '300' : item.value == 'createTime' ? '200' : ''"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="openEdit(row)">试课单</el-button>
            <el-button type="warning" size="mini" v-if="isAdmin" @click="onAssign(row.id, 0)">指派</el-button>
            <el-button type="danger" size="mini" v-if="isAdmin" @click="onForceAssign(row.id, 1)">强制指派</el-button>
            <el-button type="warning" size="mini" v-if="!isAdmin && !isTeamLeader" @click="addGroup(row.id)">指派</el-button>
            <!-- <el-button type="danger" size="mini" v-if="!isAdmin&&row.studentSource=='系统派单'"
              @click="onReject(row)">拒绝</el-button> -->
          </div>
          <el-row :gutter="20" type="flex" justify="center" align="middle" v-else-if="item.value == 'createTime'">
            <el-col :span="18" :offset="0">
              <div>{{ row[item.value] }}</div>
            </el-col>
            <el-col :span="6" :offset="0" v-if="isAdmin">
              <el-button type="text" @click="getDetail(row.id)">详情</el-button>
            </el-col>
          </el-row>
          <span v-else-if="item.value == 'residueTime'">
            <statistic
              v-if="row.timeOut && getTrueTime(row.timeOut)"
              ref="statistic"
              format="HH:mm:ss"
              :valueClass="statusClass(row.timeOut)"
              :value="getResidueTime(row['timeOut'])"
              :key="'statistic' + row['timeOut']"
              time-indices
            ></statistic>
            <span v-else>{{ '-' }}</span>
          </span>
          <span v-else-if="item.value == 'deliverSource'">
            <span v-if="row.deliverSource != 0">{{ getSourse(row.deliverSource) }}</span>
            <span v-else>{{ '-' }}</span>
          </span>
          <span v-else-if="item.value == 'referrerPhone'">
            <span>{{ telHide(row.referrerPhone) }}</span>
          </span>
          <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="querydata.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 修改试课单 -->
    <!-- v-if="editForm.curriculumCode !== 'MATH'" -->
    <el-dialog
      v-if="MATHCurriculumCodeArr.indexOf(editForm.curriculumCode) == -1"
      :visible.sync="editList"
      top="2vh"
      :close-on-click-modal="false"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      title="试课单"
      @close="closeEdit"
    >
      <el-form ref="editForm" :model="editForm" :label-width="screenWidth > 1300 ? '150px' : '110px'">
        <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.realName" disabled />
        </el-form-item>
        <el-form-item label="区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-cascader v-model="editForm.address" :options="options02" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item v-if="editForm.curriculumName == '鼎英语'" label="英语分数" prop="score" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.score" placeholder="" :disabled="true" />
        </el-form-item>
        <el-form-item label="课程类型" prop="curriculumName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.curriculumName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="预约试课时间" prop="expectTime">
          <div style="display: flex; align-items: center">
            <el-form-item prop="dateslot">
              <trialDate v-if="addVisible" ref="triaTime" @onChild="onChild" :dateTime="dateTime" :disabled="isTeamLeader"></trialDate>
            </el-form-item>
            <el-form-item prop="timeslot">
              <el-select v-model="timeslot" placeholder="请选择试课时间段" style="margin-left: 10px" :disabled="isTeamLeader" @change="changeTimeslot">
                <el-option v-for="(item, index) in timeList" :key="index" :label="item.startTime + '~' + item.endTime" :value="item.startTime"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="试课对象" prop="experienceObject" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.experienceObject" :disabled="isTeamLeader">
            <el-radio :label="1">B端</el-radio>
            <el-radio :label="2">C端</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="客户姓名" prop="clientName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.clientName" placeholder="请输入" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="咨询师" prop="counselor" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.counselor" :disabled="isTeamLeader">
            <el-radio :label="'1'">上级推荐人</el-radio>
            <el-radio :label="'0'">自己</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="editForm.curriculumName == '鼎英语'" label="英语课外辅导" prop="classInstruction" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.classInstruction" :disabled="isTeamLeader">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="体验需求" prop="remark" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.remark" type="textarea" placeholder="请输入" :rows="5" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-select v-model="editForm.grade" placeholder="请选择" :disabled="isTeamLeader">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐人姓名" prop="referrerName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerName" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerPhone" :style="{ width: screenWidth > 1300 ? '83%' : '100%' }" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="提交时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-date-picker readonly v-model="editForm.createTime" type="datetime" placeholder="选择日期时间" style="width: 100%" :disabled="isTeamLeader"></el-date-picker>
        </el-form-item>
        <el-form-item label="客户类型" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.customerType" :disabled="isTeamLeader">
            <el-radio label="1">家长</el-radio>
            <el-radio label="2">意向客户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否成交" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.isDeal" :disabled="isTeamLeader">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成交金额" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input-number :min="0" :max="9999999" v-model="editForm.dealPrice" style="width: 150px" :disabled="isTeamLeader" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh" v-if="isTeamLeader">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeEdit">确定</el-button>
      </el-row>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh" v-else>
        <el-button type="primary" plain style="margin-right: 1.5vw" size="small" @click="closeEdit">取消</el-button>
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="editTestForm">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 数学试课单 -->
    <!-- v-if="editForm.curriculumCode === 'MATH'" -->
    <el-dialog
      v-if="MATHCurriculumCodeArr.indexOf(editForm.curriculumCode) != -1"
      :visible.sync="editList"
      top="2vh"
      :close-on-click-modal="false"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      title="数学试课单"
      @close="closeEdit"
    >
      <el-form disabled ref="editForm" :model="editForm" :label-width="screenWidth > 1300 ? '150px' : '110px'">
        <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.realName" disabled />
        </el-form-item>
        <el-form-item label="区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-cascader v-model="editForm.address" :options="options02" disabled />
        </el-form-item>
        <el-form-item v-if="editForm.curriculumName == '鼎英语'" label="英语分数" prop="score" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.score" placeholder="" :disabled="true" />
        </el-form-item>
        <el-form-item label="课程类型" prop="curriculumName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.curriculumName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="预约试课时间" prop="expectTime">
          <div style="display: flex; align-items: center">
            <el-form-item prop="dateslot">
              <trialDate v-if="addVisible" ref="triaTime" @onChild="onChild" :dateTime="dateTime" :disabled="isTeamLeader"></trialDate>
            </el-form-item>
            <el-form-item prop="timeslot">
              <el-select v-model="timeslot" placeholder="请选择试课时间段" style="margin-left: 10px" :disabled="isTeamLeader" @change="changeTimeslot">
                <el-option v-for="(item, index) in timeList" :key="index" :label="item.startTime + '~' + item.endTime" :value="item.startTime"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="试课对象" prop="experienceObject" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.experienceObject" :disabled="isTeamLeader">
            <el-radio :label="1">B端</el-radio>
            <el-radio :label="2">C端</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="客户姓名" prop="clientName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.clientName" placeholder="请输入" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="咨询师" prop="counselor" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.counselor" :disabled="isTeamLeader">
            <el-radio :label="'1'">上级推荐人</el-radio>
            <el-radio :label="'0'">自己</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="editForm.curriculumName == '鼎英语'" label="英语课外辅导" prop="classInstruction" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.classInstruction" :disabled="isTeamLeader">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="体验需求" prop="remark" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.remark" type="textarea" placeholder="请输入" :rows="5" :disabled="isTeamLeader" />
        </el-form-item>
        <!-- 新加 -->
        <el-form-item label="版本" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.versionName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="学科" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.disciplineName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="当前年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.gradeName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-select v-model="editForm.grade" placeholder="请选择" :disabled="isTeamLeader">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐人姓名" prop="referrerName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerName" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerPhone" :style="{ width: screenWidth > 1300 ? '83%' : '100%' }" :disabled="isTeamLeader" />
        </el-form-item>
        <el-form-item label="提交时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-date-picker readonly v-model="editForm.createTime" type="datetime" placeholder="选择日期时间" style="width: 100%" :disabled="isTeamLeader"></el-date-picker>
        </el-form-item>
        <el-form-item label="客户类型" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.customerType" :disabled="isTeamLeader">
            <el-radio label="1">家长</el-radio>
            <el-radio label="2">意向客户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否成交" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.isDeal" :disabled="isTeamLeader">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成交金额" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input-number :min="0" :max="9999999" v-model="editForm.dealPrice" style="width: 150px" :disabled="isTeamLeader" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh" v-if="isTeamLeader">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeEdit">确定</el-button>
      </el-row>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh" v-else>
        <el-button type="primary" plain style="margin-right: 1.5vw" size="small" @click="closeEdit">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 指派弹框 -->
    <el-dialog
      :visible.sync="assignDialog"
      :close-on-click-modal="false"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      :title="assignForm.isForced == 1 ? '强制指派交付中心' : '指派交付中心'"
    >
      <el-form ref="form" label-width="150px">
        <el-form-item label="选择交付中心">
          <el-cascader
            v-model="assignForm.deliverMerchantCode"
            :options="deliverOptions"
            filterable
            :props="{ label: 'deliverMerchantName', value: 'deliverMerchantCode' }"
            clearable
          ></el-cascader>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeAssgin">取消</el-button>
        <el-button type="primary" style="margin-right: 1.5vw" size="small" :loading="isSubmit" @click="submitAssign">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 流转指派历史弹框 -->
    <el-dialog title="历史派单记录" :close-on-click-modal="false" :visible.sync="dialogHistory" width="30%" center>
      <div style="overflow: auto; margin: 30px 0; height: 400px" v-loading="dialogLoading">
        <!-- <ul class="infinite-list" v-infinite-scroll="load" style="overflow:auto;margin: 30px 0;height:400px">
        </ul> -->
        <div v-if="historys.length > 0">
          <el-steps direction="vertical" :active="0" :space="200">
            <el-step v-for="(item, index) in historys" :title="item.time" :key="index" icon="iconfont icon-luyin">
              <template slot="description">
                <div style="white-space: pre-wrap">{{ item.history }}</div>
              </template>
              <template slot="icon">
                <i class="el-icon-info" v-if="index == 0" style="font-size: 24px"></i>
                <i class="el-icon-success" v-else style="font-size: 24px"></i>
              </template>
            </el-step>
          </el-steps>
        </div>
        <div class="nomore" v-if="historys.length < 1">
          <el-image style="width: 6.25rem; height: 6.25rem" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
          <div style="color: #999; margin-top: 1.25rem">无数据</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHistory = false">取 消</el-button>
        <el-button type="primary" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="指派交付小组" :visible.sync="dialogAddgroup" width="30%" @close="AddgroupClose">
      <span>
        选择交付小组
        <el-select style="margin-left: 20px" v-model="groupForm.teamId" placeholder="请选择" clearable filterable>
          <el-option v-for="item in groupList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
        </el-select>
      </span>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button @click="dialogAddgroup = false">取消</el-button>
        <el-button type="primary" @click="AddgroupClick">确定</el-button>
      </span>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import { mapGetters } from 'vuex';
  import { selAllExperienceUsableTime, getCurrentHour } from '@/api/studentClass/changeList';
  import statistic from '../components/statistic.vue';
  // import { pageParamNames } from "@/utils/constants";
  import { deliverlist } from '@/api/peizhi/peizhi';
  import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue';

  import {
    getTestStudentList,
    assignTestStudent,
    rejectTsetStudent,
    getDurationConfig,
    getDurationConfigTimes,
    getHistory,
    selectTeam,
    changeTeam2,
    getLeaderTeamList,
    editExp
  } from '@/api/orderManage';
  import { getTableTitleSet, setTableList, bvstatusListOne } from '@/api/paikeManage/classCard';
  import { CodeToText, regionData, TextToCode } from 'element-china-area-data';
  import { updateDetail, queryStudentExperienceDetail, belongDeliverAndAllDeliver, GradeType } from '@/api/studentClass/changeList';
  import trialDate from '@/views/pclass/components/trialDate';
  import { MATHCurriculumCodeArr } from '@/utils/constants.js';
  export default {
    name: 'testStudent',
    components: {
      HeaderSettingsDialog,
      trialDate,
      statistic
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        MATHCurriculumCodeArr,
        isAdmin: false,
        isTeamLeader: false,
        screenWidth: window.screen.width, //屏幕宽度
        editList: false, //试课单弹窗
        editForm: {}, //试课单表单
        options: {},
        groupForm: {}, //交付小组id
        dialogAddgroup: false,
        options02: regionData,
        timeslot: '', // 期望试课时间段
        dateslot: '',
        timeList: [],
        // 搜索表单
        querydata: {
          source: '',
          studentName: '',
          studentCode: '',
          merchantCode: '',
          deliverMerchant: '',
          deliverName: '',
          dispatchStatus: '',
          curriculumId: '',
          // deliverSource: '',
          pageNum: 1,
          pageSize: 10 //页容量
        },
        // 表格数据总数量
        total: null,
        // 表格数据
        tableData: [],
        tableLoading: false,
        addVisible: false,

        option: [],
        optionTotal: 0,
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          deliverName: ''
        },
        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [],
        headerSettings1: [
          {
            name: '姓名',
            value: 'realName'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '课程类型',
            value: 'courseType'
          },
          {
            name: '试课时间',
            value: 'expectTime'
          },
          {
            name: '派单交付小组',
            value: 'teamName'
          },
          {
            name: '派单时间',
            value: 'createTime'
          },
          {
            name: '剩余接单时间',
            value: 'residueTime'
          },
          {
            name: '派单来源',
            value: 'deliverSource'
          },
          {
            name: '学生来源',
            value: 'studentSource'
          }
        ],
        headerSettings2: [
          {
            name: '姓名',
            value: 'realName'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '试课时间',
            value: 'expectTime'
          },
          {
            name: '家长联系方式',
            value: 'phone'
          },
          {
            name: '课程类型',
            value: 'courseType'
          },
          {
            name: '派单交付小组',
            value: 'teamName'
          },
          {
            name: '派单状态',
            value: 'dispatchStatus'
          },
          {
            name: '派单时间',
            value: 'createTime'
          },
          {
            name: '剩余接单时间',
            value: 'residueTime'
          },
          {
            name: '派单来源',
            value: 'deliverSource'
          },
          {
            name: '学员来源',
            value: 'studentSource'
          },
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          },
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '推荐人手机号',
            value: 'referrerPhone'
          }
          // {
          //   name: '门店账号',
          //   value: 'merchantCode'
          // },

          // {
          //   name: '门店名称',
          //   value: 'merchantName'
          // },
          // {
          //   name: '门店手机号',
          //   value: 'merchantMobile'
          // }
        ],
        deliverSources: [
          { name: '管理员指定交付中心', value: 100 },
          { name: '指定交付小组派单', value: 101 },
          { name: '续课派单', value: 102 },
          { name: '试课转正课派单', value: 103 },
          { name: '交付中心直属门店派单', value: 104 },
          { name: '推荐人门店绑定主交付中心派单', value: 105 },
          { name: '推荐人门店绑定次交付中心派单', value: 106 },
          { name: '轮排派单', value: 107 },
          { name: '没有可用交付中心', value: 108 },
          { name: '未知派单', value: -1 }
        ],
        tableHeaderList: [], // 获取表头数据
        //指派的交付中心编号
        assignForm: {
          id: '',
          deliverMerchantCode: ''
        },
        groupList: [],
        deliverOptions: [],
        assignDialog: false,
        arrangementDuration: 0, //交付中心轮排配置时间
        dialogHistory: false,
        dialogLoading: false,
        historys: [],
        isSubmit: false,
        configTime: {
          deliverTime: '',
          spareDeliverTime: '',
          transferTime: ''
        },
        leaderTeamList: [],
        rules: {
          realName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: function (rule, value, callback) {
                if (/^1[345789]\d{9}$/.test(value) == false) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          studentSource: [{ required: true, message: '请输入来源', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
          expectTime: [
            { required: true, message: '请选择时间', trigger: 'change' }
            // { validator: validateTime, trigger: "change" },
          ],
          address: [{ required: true, message: '请选择区域', trigger: 'change' }],
          gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
          referrerName: [{ required: true, message: '请输入推荐人姓名', trigger: 'blur' }],
          referrerPhone: [{ required: true, message: '请填写推荐人手机号', trigger: 'blur' }]
        },
        courseList: []
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      // this.isTeamLeader = ls.getItem('rolesVal') === 'DeliverTeamLeader';
      this.isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';
      this.headerSettings = this.isAdmin ? this.headerSettings2 : this.headerSettings1;
      this.getHeaderlist();
      this.getConfig();
      this.GradeType();
      if (this.isAdmin) {
        this.getTeacherList();
        setTimeout(() => {
          this.initData();
        }, 200);
      } else if (this.isTeamLeader) {
        this.initTeamList();
      } else {
        this.getGroupList();
        setTimeout(() => {
          this.initData();
        }, 200);
      }
    },
    watch: {
      isAdmin: function (val) {
        this.headerSettings = val ? this.headerSettings2 : this.headerSettings1;
      }
    },
    computed: {
      ...mapGetters(['code'])
    },
    mounted() {
      this.getbvstatusList();
    },
    methods: {
      //修改时间事件
      changeTimeslot(e) {
        if (e.length == 4) {
          e = '0' + e;
        }
        let time = e + ':00';
        this.editForm.expectTime = this.dateslot + ' ' + time;
      },
      async getCurrentHourFn() {
        let { data } = await getCurrentHour();
        return data.value;
      },
      async initTeamList() {
        let { data } = await getLeaderTeamList();
        this.leaderTeamList = data;
        if (this.leaderTeamList.length < 1) {
          return this.$message.warning('您暂无小组');
        } else {
          this.querydata.teamId = data.length > 0 ? data[0].id : '';
          setTimeout(() => {
            this.initNewData();
          }, 300);
        }
      },
      handlechangeDownCompany(e) {
        // console.log(e);
        this.querydata.teamId = e;
        this.initNewData();
      },
      getbvstatusList() {
        bvstatusListOne().then((res) => {
          this.courseList = res.data;
        });
      },
      // 手机号脱敏('13912345678' 转换成 '139****5678') 第3位开始替换4个
      telHide(value) {
        if (!value) {
          return '';
        } else {
          let data = value.replace(/(\d{3})\d{4}(\d*)/, '$1****$2');
          return data;
        }
      },
      getSourse(value) {
        let arr = this.deliverSources.filter((i) => i.value == value);
        if (arr.length > 0) {
          return arr[0].name;
        } else {
          return '';
        }
      },
      async GradeType() {
        let data = await GradeType();
        this.options = data.data;
        console.log(data);
      },
      async getGroupList() {
        let res = await selectTeam(this.code);
        this.groupList = res.data;
      },
      // 增加接单交付小组新增
      addGroup(id) {
        this.groupForm.id = id;
        this.groupForm.type = 1;
        this.dialogAddgroup = true;
      },
      //确定增加交付小组
      async AddgroupClick() {
        await changeTeam2(this.groupForm);
        this.$message.success('指派成功');
        this.initData();
        this.dialogAddgroup = false;
      },
      // 增加交付小组弹窗关闭
      AddgroupClose() {
        this.dialogAddgroup = false;
        this.groupForm = {};
      },
      // 派单历史
      async getDetail(id) {
        this.dialogHistory = true;
        this.dialogLoading = true;
        let { data } = await getHistory(id);
        // console.log(data)
        this.historys = data;
        setTimeout(() => {
          this.dialogLoading = false;
        }, 500);
      },
      // 动态class
      statusClass(time) {
        let normTime = 10 * 60 * 1000;
        let newTime = new Date(time).getTime() - Date.now();
        if (newTime <= normTime) {
          return 'error';
        } else {
          return 'normal';
        }
      },
      getTrueTime(time) {
        if (time) {
          let year = new Date(time).getFullYear();
          let newTime = new Date(time).getTime() - Date.now();
          if (year > 6000 || newTime < 0) {
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
      },
      // 时间转换方法
      getResidueTime(time) {
        if (time) {
          let time1 = new Date(time).getTime();
          // let time2 = Date.now()
          // let newTime = time1 - time2
          // if (newTime <= 0) {
          //   return 0
          // } else {
          //   return newTime
          // }
          return time1;
        } else {
          return 0;
        }
      },
      // 时间戳转换 HH:MM:SS
      getTimeFromTimestamp(timestamp) {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
      },
      // 获取当前轮排配置
      async getConfig() {
        // let { data } = await getDurationConfig()
        let { data } = await getDurationConfigTimes();
        this.configTime = data;
        // console.log(data, '111111111111111111111111')
        this.arrangementDuration = Number(data.arrangementDuration);
      },
      //指派方法
      async onAssign(id, isForced) {
        this.assignForm.id = id;
        this.assignForm.isForced = isForced;
        const { data } = await belongDeliverAndAllDeliver(id);
        this.deliverOptions = data.deliverList;
        this.assignDialog = true;
      },
      async onForceAssign(id, isForced) {
        this.assignForm.id = id;
        this.assignForm.isForced = isForced;
        const { data } = await belongDeliverAndAllDeliver(id);
        this.deliverOptions = data.deliverList;
        this.assignDialog = true;
      },
      // 关闭指派弹框
      closeAssgin() {
        this.assignForm = {
          id: '',
          deliverMerchantCode: '' //指派的交付中心编号
        };
        this.isSubmit = false;
        this.assignDialog = false;
      },
      // 指派提交
      async submitAssign() {
        // console.log(this.assignForm)
        this.isSubmit = true;
        let data = {};
        if (this.assignForm.deliverMerchantCode.length > 0) {
          data = {
            id: this.assignForm.id,
            deliverMerchantCode: this.assignForm.deliverMerchantCode[0],
            isForced: this.assignForm.isForced
          };
        } else {
          this.isSubmit = false;
          return this.$message.warning('请选择交付中心');
        }
        try {
          await assignTestStudent(data);
          this.isSubmit = false;
          this.$message.success('指派成功');
          this.initData();
          this.closeAssgin();
        } catch (error) {
          this.isSubmit = false;
          return this.$message.error(error);
        }

        // console.log(res)
      },
      // 拒绝指派
      async onReject(row) {
        await this.$confirm(`您确定要拒绝${row.realName}(${row.studentCode})吗`);
        await rejectTsetStudent(row.id);
        this.$message.success('拒绝成功');
        await this.initData();
      },
      // 打开试课单
      async openEdit(row) {
        console.log(row, 'row----');

        let data = await updateDetail(row.id);
        this.editForm = data.data;
        console.log('🚀 ~ openEdit ~ this.editForm:', this.editForm);
        let orderId = this.editForm.orderId;
        // if (orderId && this.editForm.curriculumCode === 'MATH') {
        if (orderId && this.MATHCurriculumCodeArr.indexOf(this.editForm.curriculumCode) != -1) {
          let dataMath = await queryStudentExperienceDetail(orderId);
          // this.editForm = dataMath.data.addExpDto; //获取数学基础表单数据
          this.editForm.versionName = dataMath.data.mathExtendedFieldDto.versionName;
          this.editForm.disciplineName = dataMath.data.mathExtendedFieldDto.disciplineName;
          this.editForm.gradeName = dataMath.data.mathExtendedFieldDto.gradeName;
          console.log('🚀 ~ openEdit ~ data22:', dataMath);
        }
        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }
        if (!this.editForm.expectTime) {
          this.$message({
            showClose: true,
            message: '请先填写试课单',
            type: 'error'
          });
          return;
        }
        console.log(this.editForm);
        this.addVisible = true;
        this.dateTime = this.editForm.expectTime.slice(0, 10);
        let times = this.editForm.expectTime.split(' ');
        let time;
        if (Number(times[1].slice(3, 5)) >= 30) {
          time = '30';
        } else {
          // console.log(Number(times[1].slice(3, 5)));
          time = '00';
        }
        this.timeslot = times[1].slice(0, 3) + time;
        if (this.timeslot[0] == '0') {
          this.timeslot = this.timeslot.slice(1);
        }
        await this.getReservationTime();
        if (data.data.customerType == 1) {
          this.editForm.customerType = '1';
        } else if (data.data.customerType == 2) {
          this.editForm.customerType = '2';
        }
        if (data.data.isAsk == 1) {
          this.editForm.isAsk = '1';
        } else if (data.data.isAsk == 2) {
          this.editForm.isAsk = '2';
        }
        if (data.data.isDeal == 1) {
          this.editForm.isDeal = '1';
        } else if (data.data.isDeal == 2) {
          this.editForm.isDeal = '2';
        }
        this.editForm.grade = String(this.editForm.grade == 0 ? 1 : this.editForm.grade);
        if (this.editForm.province && this.editForm.city && this.editForm.area) {
          let arr = [];
          arr.push(this.editForm.province);
          arr.push(this.editForm.city);
          arr.push(this.editForm.area);
          if (this.editForm.province === this.editForm.city) {
            this.editForm.city = '市辖区';
          }
          if (TextToCode[this.editForm.province]) {
            this.editForm.address = TextToCode[this.editForm.province].code;
            if (TextToCode[this.editForm.province][this.editForm.city]) {
              this.editForm.address = TextToCode[this.editForm.province][this.editForm.city].code;
              if (TextToCode[this.editForm.province][this.editForm.city][this.editForm.area]) {
                this.editForm.address = TextToCode[this.editForm.province][this.editForm.city][this.editForm.area].code;
              }
            }
          }
        }
        this.editList = true;
      },
      onChild(e) {
        console.log('获取子组件传过来的值：', e);
        this.dateslot = e;
        if (this.dateslot != '' && this.timeslot != '') {
          this.editForm.expectTime = this.dateslot + ' ' + this.timeslot + ':00';
          console.log(this.editForm.expectTime);
        }
      },
      // 获取所有可预约时间段人数
      async getReservationTime() {
        await selAllExperienceUsableTime()
          .then((res) => {
            console.log(res);
            this.timeList = res.data;
          })
          .catch((err) => {});
      },
      async editTestForm() {
        let that = this;
        let hour = await that.getCurrentHourFn();
        console.log(this.editForm.expectTime.length, '111111111111111');

        if (this.editForm.expectTime.length == 18) {
          let a = this.editForm.expectTime.split(' ');
          this.editForm.expectTime = a[0] + ' 0' + a[1];
        }
        this.editForm.province = CodeToText[this.editForm.address[0]];
        this.editForm.city = CodeToText[this.editForm.address[1]];
        this.editForm.area = CodeToText[this.editForm.address[2]];
        const h = that.$createElement;
        that
          .$confirm('提示', {
            title: '提示',
            message: h('p', null, [h('div', null, '是否修改该试课单 ')]),
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(async () => {
            if (Date.now() + hour * 60 * 60 * 1000 > new Date(that.editForm.expectTime).getTime()) {
              this.editForm.expectTime = '';
              return that.$message({
                showClose: true,
                message: `预约试课时间不得小于${hour}小时`,
                type: 'error'
              });
            }
            let b = that.editForm.score.split('/');
            if (b.length > 1) {
              that.editForm.score = b[0];
            }
            if (!that.editForm.score && that.editForm.curriculumName == '鼎英语') that.editForm.score = 0;
            that.$refs.editForm.validate(async (valid) => {
              if (valid) {
                await editExp(that.editForm).then(() => {
                  that.$message.success('操作成功');
                  that.closeEdit();
                  that.initData();
                });
              } else {
                return false;
              }
            });
          })
          .catch(() => {
            // that.closeEdit()
          });
      },
      closeEdit() {
        this.$refs.editForm.clearValidate();
        this.editList = false;
        this.addVisible = false;
      },
      searchData() {
        this.querydata.pageNum = 1;
        this.querydata.pageSize = 10;
        this.initData();
      },
      async initNewData() {
        let that = this;
        that.tableLoading = true;
        if (that.leaderTeamList.length < 1) {
          that.tableData = [];
        } else {
          if ((this.querydata.curriculumId && this.querydata.curriculumId.length != 19) || !this.querydata.curriculumId) {
            delete this.querydata.curriculumId;
          }
          let { data } = await getTestStudentList(this.querydata);
          // console.log(data, '=======================')
          this.tableData = data.data;
          this.total = Number(data.totalItems);
          // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
          that.tableLoading = false;
          this.$forceUpdate();
        }
        that.tableLoading = false;
        this.$forceUpdate();
      },
      // 初始化表格
      async initData() {
        let that = this;
        that.tableLoading = true;
        if ((this.querydata.curriculumId && this.querydata.curriculumId.length != 19) || !this.querydata.curriculumId) {
          delete this.querydata.curriculumId;
        }
        let { data } = await getTestStudentList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
        that.tableLoading = false;
        this.$forceUpdate();
      },
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },

      //重置
      rest() {
        this.$refs.querydata.resetFields();
        this.querydata = {
          studentName: '',
          studentCode: '',
          merchantCode: '',
          deliverMerchant: '',
          deliverName: '',
          dispatchStatus: '',
          curriculumId: '',
          pageNum: 1,
          pageSize: 10 //页容量
        };
        if (this.isTeamLeader) {
          this.querydata.teamId = this.leaderTeamList.length ? this.leaderTeamList[0].id : '';
        }
        this.initData();
      },
      // 获取交付中心
      async getTeacherList() {
        let allData = await deliverlist(this.selectObj);
        this.option = this.option.concat(allData.data.data);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 改变交付中心编号事件
      changeInput(e) {
        if (!!e) {
          let arr = this.option.filter((i) => i.deliverMerchantCode == e);
          this.querydata.deliverName = arr.length > 0 ? arr[0].deliverMerchantName : this.querydata.deliverName;
        }
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 清除交付中心名称事件
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.deliverName == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.deliverName = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      // 改变交付中心名称事件
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.querydata.deliverCode = '';
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        } else {
          let arr = this.option.filter((i) => i.deliverMerchantName == e);
          this.querydata.deliverCode = arr[0].deliverMerchantCode;
        }
      },
      changeMessage() {
        this.$forceUpdate();
      },
      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 表头设置
      headerList() {
        if (this.tableHeaderList.length > 0) {
          // console.log(this.tableHeaderList)
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {};
        data = this.isAdmin
          ? {
              type: 'testStudent-admin',
              value: JSON.stringify(arr)
            }
          : {
              type: 'testStudent',
              value: JSON.stringify(arr)
            };
        this.setHeaderSettings(data);
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {};
        data = this.isAdmin ? { type: 'testStudent-admin' } : { type: 'testStudent' };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            let Json = this.removeNullValues(res.data.value);
            // console.log(Json)
            this.tableHeaderList = JSON.parse(Json);
            // this.tableHeaderList = JSON.parse(res.data.value)
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 设置表头
      async setHeaderSettings(data) {
        console.log(data);
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  body {
    background-color: #f5f7fa;
  }

  .frame {
    // margin:  0 30px;
    background-color: rgba(255, 255, 255);
    padding: 20px;
  }

  .el-button--success {
    color: #ffffff;
    background-color: #6ed7c4;
    border-color: #6ed7c4;
  }

  .transferred {
    color: #ea2424;
  }

  .no_transferred {
    color: #1cb31c;
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
