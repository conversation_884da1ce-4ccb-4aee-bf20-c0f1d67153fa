<!-- 一对多-学习课程表-数据查看弹窗 -->
<template>
  <el-drawer :title="title" :visible="dataLookVisible" @close="handleClose" style="margin-top: 15px" :size="screenWidth > 1300 ? '35%' : '70vw'">
    <el-row v-loading="loading" class="paikeTwo" style="margin-top: 1vw">
      <el-col class="paike">班级名称：{{ dataList.className }}</el-col>
      <el-col class="paike">班级编号：{{ dataList.classCode }}</el-col>
      <el-col class="paike">年级：{{ dataList.gradeName }}</el-col>
      <el-col class="paike">日期：{{ dataList.studyDate }}</el-col>
      <el-col class="paike">上课用时：{{ dataList.studyTime }}</el-col>
      <el-col class="paike">
        授课视频:
        <el-select v-model="value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-col>
      <el-col class="paike">
        今日学习内容：
        <div v-if="dataList.studyContent" class="card">{{ dataList.studyContent }}</div>
      </el-col>
      <el-col v-for="(item, index) in dataList.deliverClassStudentList" :key="index" class="paike">
        {{ item.studentName }}学习反馈：
        <div class="card">{{ item.feedback }}</div>
      </el-col>
      <el-col class="paike" style="text-align: center">
        <el-button type="primary" size="large" @click="handleClose">确定</el-button>
      </el-col>
    </el-row>
  </el-drawer>
</template>

<script>
  export default {
    name: 'DataLookDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      dataList: {
        type: Object,
        default: () => {
          return {};
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        screenWidth: window.screen.width,
        title: '数据查看',
        value: '',
        options: []
      };
    },
    computed: {
      dataLookVisible: {
        get() {
          return this.visible;
        },
        set(v) {
          this.$emit('update:visible', v);
        }
      }
    },
    methods: {
      // 关闭弹窗
      handleClose() {
        this.dataLookVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .diyih {
    display: flex;
    justify-content: center;
    margin-bottom: 3vh;
  }

  .firste {
    font-size: 18px;
    font-weight: 900;
    margin-left: 5vw;
  }

  .Second {
    padding-left: 60px;
  }

  // 2行
  .dierr {
    display: flex;
    justify-content: space-between;
  }

  .nob {
    border: none;
    background: #fff;

    &:first-child {
      padding-left: 3vw;
    }

    &:last-child {
      padding-right: 3vw;
    }
  }

  .paike {
    margin-bottom: 20px;

    &:first-child {
      margin-top: 0.5vw;
    }
  }

  .card {
    padding: 10px;
    margin-top: 6px;
    border-radius: 10px;
    line-height: 25px;
    background-color: #ebeef5;
  }

  .paikeTwo {
    width: 80%;
    margin-left: 2vw;
  }

  .xubtn {
    margin-top: 10vh;
  }

  .cc {
    height: 0px;
    margin: 1vh 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }

  .active1 {
    font-weight: 900;
    font-size: 18px;
    border-bottom: 2px solid #000;
  }

  .dateArrClass > div ::after {
    content: '';
    position: absolute;
    right: 9px;
    top: 21px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #fc3c39;
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-radius: 25px;
    height: 100%;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    // font-size: 22px;
    font-size: 30px; // 拼音法
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
    padding-top: 0;
    margin-top: 15px;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
