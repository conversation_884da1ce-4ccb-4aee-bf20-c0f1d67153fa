<!-- 交付中心-一对多学员管理-学员列表-试课学员列表 -->
<template>
  <div>
    <!-- 查询栏 -->
    <el-card class="frame" shadow="never">
      <el-form label-width="122px" ref="searchNum" :model="searchNum" :inline="true" style="display: flex; flex-wrap: wrap">
        <el-form-item label="姓名:" prop="studentName" style="display: flex">
          <el-input v-model.trim="searchNum.studentName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="学员编号:" prop="studentCode" style="display: flex">
          <el-input v-model.trim="searchNum.studentCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="订单号:" prop="orderId" style="display: flex">
          <el-input v-model.trim="searchNum.orderId" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="教练老师:" prop="teacherId" style="display: flex">
          <BaseElSelectLoadmore v-model="searchNum.teacherId" valueProp="id" labelProp="name" :searchFunc="getTeacherList"></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="推荐人:" prop="referrerNameOrPhone" style="display: flex">
          <el-input v-model.trim="searchNum.referrerNameOrPhone" clearable placeholder="请输入推荐人姓名或手机号"></el-input>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId" style="display: flex">
          <el-select v-model="searchNum.curriculumId" filterable clearable placeholder="请选择">
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="isAdmin">
          <el-form-item label="交付中心编号:" prop="deliverMerchant">
            <el-input v-model.trim="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号"></el-input>
          </el-form-item>
          <el-form-item label="交付中心名称:" prop="deliverName">
            <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="课程状态:" prop="courseStatus">
          <el-select v-model="searchNum.courseStatus" clearable placeholder="请选择">
            <el-option v-for="(item, index) in courseStatusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级:" prop="classId">
          <BaseElSelectLoadmore v-model="searchNum.classId" valueProp="id" labelProp="className" :searchFunc="getAllClassList"></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="时间:" prop="searchTimeRange">
          <el-date-picker
            v-model="searchTimeRange"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 360px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      ref="configurationTable"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 640 : 160"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }" v-if="needTableSlotProp.includes(item.value)">
          <div v-if="item.value == 'courseStatus'">
            <el-tag :type="courseStatusFormat(row.courseStatus, 'type')">{{ courseStatusFormat(row.courseStatus) }}</el-tag>
          </div>
          <div v-else-if="item.value == 'grade'">{{ commonFormat(gradeList, row[item.value]) }}</div>
          <div v-else-if="['className', 'deliverName', 'deliverMerchant'].includes(item.value)">{{ row[item.value] || '无' }}</div>
          <div v-else-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="handleAssignClassClick(row)">更换班级</el-button>
            <el-button type="primary" size="mini" @click="handleCourseScheduleClick(row)">查看课程表</el-button>
            <el-button type="primary" size="mini" @click="handleLessonTestReportClick(row)">试课报告</el-button>
            <el-button type="warning" size="mini" @click="handleClassOrderInfoClick(row.id)">查看试课单</el-button>
            <el-button type="primary" size="mini" @click="handleHolidayClick(row)">请假</el-button>
            <el-button type="primary" size="mini" @click="handleHolidayInfoClick(row)">请假记录</el-button>
            <el-button type="primary" size="mini" @click="editInfo(row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 更换班级弹窗 -->
    <StudentListAssignClassDialog :visible.sync="assignClassDialogVisible" :value-data="assignClassFormData" :course-type="1" @submit="handleAssignClassSubmitClick" />

    <!-- 试课报告弹窗 -->
    <StudyScheduleDataLook :visible.sync="testReportDialogVisible" :loading="testReportLoading" :data-list="testReportData" ref="StudyScheduleDataLook" />
    <StudyScheduleMathDataLook :visible.sync="testReportDialogMathVisible" :loading="testReportMathLoading" :data-list="testReportMathData" ref="StudyScheduleMathDataLook" />

    <!-- 试课单弹窗 -->
    <el-dialog title="试课单" :visible.sync="classOrderInfoDialogVisible" width="60%" :before-close="handleClassOrderInfoDialogClose">
      <div>
        <TrialClass v-if="classOrderInfoId" :id="classOrderInfoId" @close="handleClassOrderInfoDialogClose" />
        <div style="text-align: center">
          <el-button type="primary" size="large" @click="handleClassOrderInfoDialogClose">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!--请假弹框-->
    <holiday-dialog v-model="holidayVisible" :courseType="courseType" :holidayRow="holidayRow" @submit="refreshTable" />
    <!-- 请假记录弹框-->
    <holiday-record-dialog v-model="holidayRecordVisible" :courseType="courseType" :holidayRecordRow="holidayRecordRow" />
    <!-- 表头设置弹窗 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
    <!-- 编辑弹框 -->
    <EditClassInfo ref="EditClassInfo" @editClassInfo="handleEditClassInfo"></EditClassInfo>
  </div>
</template>

<script>
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import BaseElSelectLoadmore from './components/studentList/BaseElSelectLoadmore.vue';
  import StudentListAssignClassDialog from './components/studentList/StudentListAssignClassDialog.vue';
  import StudyScheduleDataLook from './components/studySchedule/StudyScheduleDataLook.vue';
  import StudyScheduleMathDataLook from './components/studySchedule/StudyScheduleMathDataLook.vue';
  import TrialClass from './components/TrialClass.vue';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import { selAllTeacher, GradeType } from '@/api/studentClass/changeList';
  import { getHistory } from '@/api/orderManage';
  import { getFeedbackInfo, getMathFeedbackInfoExp } from '@/api/oneToManyClass/studySchedule';
  import { getAllClass, getStudentTrialData, setAssignClassData } from '@/api/oneToManyClass/studentList';
  import ls from '@/api/sessionStorage';
  import HolidayDialog from '@/views/oneToManyClass/components/holidayClass/holidayDialog.vue';
  import HolidayRecordDialog from '@/views/oneToManyClass/components/holidayClass/holidayRecordDialog.vue';
  import EditClassInfo from '@/views/oneToManyClass/components/studentList/editClassInfo.vue';
  import { MATHCurriculumCodeArr } from '@/utils/constants.js';
  export default {
    name: 'StudentListTrial',
    props: {
      courseType: {
        //1试课 2正课
        type: String,
        default: ''
      }
    },
    components: {
      HolidayRecordDialog,
      HolidayDialog,
      HeaderSettingsDialog,
      BaseElSelectLoadmore,
      StudentListAssignClassDialog,
      StudyScheduleDataLook,
      StudyScheduleMathDataLook,
      TrialClass,
      EditClassInfo
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        MATHCurriculumCodeArr,
        isAdmin: false,
        isDeliveryCenter: false,

        // 搜索栏
        searchNum: {
          courseStatus: ''
        },
        searchTimeRange: [],
        curriculumList: [],
        courseStatusList: [
          { label: '全部', value: '' },
          { label: '待排课', value: '1' },
          { label: '已排课', value: '2' },
          { label: '已完成', value: '3' }
        ],

        // 获取表头数据
        tableHeaderList: [],

        // 列表属性弹框
        HeaderSettingsStyle: false,
        headerSettings: [
          { name: '姓名', value: 'realName' },
          { name: '学员编号', value: 'studentCode' },
          { name: '期望上课时间', value: 'extendTime' },
          { name: '所属交付小组', value: 'teamName' },
          { name: '课程类型', value: 'courseType' },
          { name: '课程状态', value: 'courseStatus' },
          { name: '年级', value: 'grade' },
          { name: '班级', value: 'className' },
          { name: '联系方式', value: 'phone' },
          { name: '教练老师', value: 'teacherName' },
          { name: '体验需求', value: 'remark' },
          { name: '订单号', value: 'orderNo' },
          { name: '推荐人', value: 'referrerName' },
          { name: '推荐人手机号', value: 'referrerPhone' },
          { name: '操作', value: 'operate' }
        ],

        // 表格数据
        tableLoading: false,
        needTableSlotProp: ['courseStatus', 'className', 'deliverName', 'deliverMerchant', 'grade', 'operate'],
        tableList: [],
        gradeList: [],
        courseStatusTableDict: [
          { label: '待排课', value: '待排课', type: 'warning' },
          { label: '已排课', value: '已排课', type: 'primary' },
          { label: '待上课', value: '待上课', type: 'warning' },
          { label: '已完成', value: '已完成', type: 'success' }
        ],
        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },

        // 更换班级弹框数据
        assignClassDialogVisible: false,
        assignClassFormData: {},

        // 试课报告弹窗数据
        testReportDialogVisible: false,
        testReportDialogMathVisible: false,
        testReportLoading: false,
        testReportMathLoading: false,
        testReportData: {},
        testReportMathData: {},

        // 试课单弹窗数据
        classOrderInfoDialogVisible: false,
        classOrderInfoId: '',
        // 请假相关
        holidayVisible: false,
        holidayRecordVisible: false,
        holidayRow: {},
        holidayRecordRow: {}
      };
    },
    created() {
      // 是否是管理员(dxAdmin+187)
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';

      // 是否是交付中心
      this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';

      if (this.isAdmin) {
        this.headerSettings.splice(6, 0, { name: '交付中心名称', value: 'deliverName' }, { name: '交付中心编号', value: 'deliverMerchant' });
      }

      // 获取表头设置
      this.getHeaderlist();
    },
    mounted() {
      this.getCurriculumList();
      this.getGradeList();
      this.getStudentTrialList();
    },
    methods: {
      // 获取教练老师下拉列表
      getTeacherList(selectQuery) {
        return new Promise((resolve, reject) => {
          selAllTeacher(selectQuery)
            .then((res) => {
              resolve(
                res.data.data.map((item) => {
                  return { value: item.value, label: item.label };
                })
              );
            })
            .catch((err) => {
              reject(err);
            });
        });
      },
      // 获取班级下拉列表
      getAllClassList(selectQuery) {
        return new Promise((resolve, reject) => {
          getAllClass({ ...selectQuery, type: 1 })
            .then((res) => {
              const allClass = res.data.data || [];
              resolve(
                allClass.map((item) => {
                  return {
                    label: item.className,
                    value: item.id
                  };
                })
              );
            })
            .catch((err) => {
              reject(err);
            });
        });
      },
      // 获取课程类型下拉列表
      getCurriculumList() {
        getAllOneToManyCurriculumType().then((res) => {
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return {
              value: item.value,
              label: item.label
            };
          });
        });
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      // 同步列表属性弹窗开启状态
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'StudentListTrial',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'StudentListTrial'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = [];
            JSON.parse(res.data.value).forEach((item) => {
              if (item) {
                this.tableHeaderList.push(item);
              }
            });
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 表格动态class
      getRowClass({ rowIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getStudentTrialList();
      },
      // 重置
      reset() {
        this.searchTimeRange = [];
        this.searchNum = { courseStatus: '' };
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getStudentTrialList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getStudentTrialList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getStudentTrialList();
      },
      // 分页查询一对多试课学员列表
      getStudentTrialList() {
        this.tableLoading = true;
        if (this.searchTimeRange.length == 2) {
          this.searchNum.startTime = this.searchTimeRange[0];
          this.searchNum.endTime = this.searchTimeRange[1];
        }
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getStudentTrialData(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data;
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => (this.tableLoading = false));
      },
      // 处理更换班级点击事件
      handleAssignClassClick(row) {
        this.assignClassFormData = { ...row, classId: '' };
        this.assignClassFormData.gradeName = this.commonFormat(this.gradeList, row.grade);
        this.assignClassFormData.oldClassId = row.classId;
        console.log('this.assignClassFormData', this.assignClassFormData);
        this.assignClassDialogVisible = true;
      },
      // 处理更换班级弹窗确定点击事件
      handleAssignClassSubmitClick(assignClassFormData) {
        let submitData = {
          flag: true,
          oldClassId: assignClassFormData.oldClassId,
          studentId: assignClassFormData.id,
          studentCode: assignClassFormData.studentCode,
          studentName: assignClassFormData.realName,
          classId: assignClassFormData.classId,
          merchantCode: assignClassFormData.merchantCode,
          merchantName: assignClassFormData.merchantName,
          curriculumId: assignClassFormData.curriculumId
        };
        if (submitData.oldClassId == submitData.classId) {
          this.$message.error('更换的班级不可与当前班级一致');
          return;
        }
        console.log('更换班级弹窗提交数据：', submitData);
        setAssignClassData(submitData).then(() => {
          this.$message.success('操作成功');
          this.assignClassDialogVisible = false;
          this.getStudentTrialList();
        });
      },
      // 处理查看课程表点击事件
      handleCourseScheduleClick(row) {
        if (row.classCode) {
          this.$router.push({
            path: '/oneToManyClass/studySchedule',
            query: {
              classCode: row.classCode
            }
          });
        } else {
          this.$message.warning('该学生没有所属班级');
        }
      },
      // 处理试课报告点击事件
      handleLessonTestReportClick(row) {
        // if (row.curriculumCode !== 'MATH') {
        console.log(this.MATHCurriculumCodeArr.indexOf(row.curriculumCode) == -1);

        if (this.MATHCurriculumCodeArr.indexOf(row.curriculumCode) == -1) {
          const { id, grade } = row;
          this.testReportDialogVisible = true;
          this.testReportLoading = true;
          getFeedbackInfo({ id })
            .then((res) => {
              if (!res.data) {
                this.testReportDialogVisible = false;
                return;
              }
              this.testReportData = res.data;

              // 年级名称取分页的数据
              this.testReportData.gradeName = this.commonFormat(this.gradeList, grade);
              let studentNames = '';
              this.testReportData.deliverClassStudentList.forEach((each) => {
                studentNames += each.studentName + '（' + each.studentCode + '）、';
              });
              this.testReportData.studentNames = studentNames.substring(0, studentNames.length - 1);
              this.testReportLoading = false;
            })
            .catch(() => {
              this.testReportDialogVisible = false;
            });
        } else {
          this.testReportDialogMathVisible = true;
          this.testReportMathLoading = true;
          getMathFeedbackInfoExp({ id: row.id, isShow: 1, courseId: '', courseType: '' })
            .then((res) => {
              console.log('数学反馈', res.data);
              if (!res.data) {
                this.testReportDialogMathVisible = false;
                this.$message.error('没有获取到排课学习时间信息');
                return;
              }
              this.testReportMathData = res.data;
              this.testReportMathLoading = false;
            })
            .catch((err) => {
              console.error('获取数学反馈信息失败', err);
              this.testReportDialogMathVisible = false;
            });
        }
      },
      // 处理试课单按钮点击事件
      handleClassOrderInfoClick(id) {
        this.classOrderInfoId = id;
        this.classOrderInfoDialogVisible = true;
      },
      // 处理试课单弹窗关闭
      handleClassOrderInfoDialogClose() {
        if ((this.classOrderInfoDialogVisible = true)) {
          setTimeout(() => {
            this.classOrderInfoId = '';
          }, 500);
        }
        this.classOrderInfoDialogVisible = false;
      },
      courseStatusFormat(value, prop = 'label') {
        return this.courseStatusTableDict.find((item) => item.value == value)?.[prop];
      },
      commonFormat(array, value) {
        let item = array.find((item) => item.value == value);
        if (item) {
          return item.label;
        }
      },
      //点击请假按钮
      handleHolidayClick(row) {
        // courseStatusNum  课程状态 1 未排课 2 已排课 3己完成
        if (row.courseStatusNum === 3) {
          return this.$message.error('该学员该班级下没有可请假课程时间');
        }
        if (row.courseStatusNum === 1) {
          return this.$message.error('该学员该班级下没有可请假课程时间');
        }
        this.holidayRow = row;
        this.holidayVisible = true;
      },
      // 点击请假记录按钮
      handleHolidayInfoClick(row) {
        this.holidayRecordRow = row;
        this.holidayRecordVisible = true;
      },
      refreshTable() {
        this.getStudentTrialList();
      },
      editInfo(row) {
        this.$refs.EditClassInfo.open(row);
      },

      handleEditClassInfo() {
        this.search();
      }
    }
  };
</script>

<style></style>
