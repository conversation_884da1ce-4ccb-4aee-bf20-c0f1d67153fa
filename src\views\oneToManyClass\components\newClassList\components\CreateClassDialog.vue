<template>
  <el-dialog :title="`创建班级`" :visible.sync="visible" destroy-on-close append-to-body width="40%" :before-close="handleClose">
    <el-row>
      <el-form label-width="120px" label-position="left" style="text-align: left" ref="form" :model="form" :rules="rules" class="my-form">
        <el-form-item label="课程类型：" prop="curriculumId">
          <el-select v-model="form.curriculumId" class="full-width" placeholder="请选择" disabled>
            <el-option v-for="item in courseList" :key="item.enCode" :label="item.enName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-select v-model="form.gradeList" class="full-width" placeholder="请选择" multiple>
            <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上课时间：" prop="classTime">
          <!--isExperience false 为2 为正课-->
          <BaseClassStudyTimeSelect
            class="full-width"
            v-model="form.classTimeConfigId"
            :isExperience="false"
            :curriculumId="form.curriculumId"
            :grade="form.grade"
            @change="handleClassTimeConfigIdChange"
          ></BaseClassStudyTimeSelect>
        </el-form-item>
        <el-form-item label="班级名称：">
          <el-input class="full-width" placeholder="班级名称系统生成" disabled></el-input>
        </el-form-item>
        <el-form-item label="包含学员:" prop="studentList">
          <el-select class="full-width" v-model="form.studentList" placeholder="请选择" multiple>
            <el-option v-for="item in studentOptions" :key="`${item.id}_${item.studentCode}`" :label="item.studentName" :value="item.studentCode">
              <span style="float: left">{{ `${item.studentName} (${item.studentCode})` }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import BaseClassStudyTimeSelect from '../../pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';
  import { addTemporaryClass } from '@/api/oneToManyClass/WaitingBuildingClass';
  import { WEEK_LIST } from '@/utils/enums';
  export default {
    name: 'CreateClassDialog',
    components: { BaseClassStudyTimeSelect },
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      courseList: {
        type: Array,
        default: () => []
      },
      gradeList: {
        type: Array,
        default: () => []
      },
      studentOptions: {
        type: Array,
        default: () => []
      },
      selectedStudents: {
        type: Array,
        default: () => []
      }
    },
    data() {
      const classTimeValidate = (rule, value, callback) => {
        if (this.form.startTime && this.form.endTime && this.form.dayOfWeek) {
          callback();
        } else {
          callback(new Error('请填写上课时间'));
        }
      };

      const studentListValidate = (rule, value, callback) => {
        if (this.form.studentList.length > 0) {
          callback();
        } else {
          callback(new Error('请至少选择一个学员'));
        }
      };

      return {
        form: {
          studentList: [],
          courseName: '',
          week: [],
          grade: '',
          startTime: '',
          endTime: '',
          limitNum: [],
          gradeList: [],
          classTimeConfigId: '',
          curriculumId: '',
          type: 2,
          deliverMerchantList: [], //交付中心编号
          courseContent: '' // 课程内容
        },
        weekOptions: WEEK_LIST,
        rules: {
          grade: {
            required: true,
            message: '请选择年级',
            trigger: 'change'
          },
          week: {
            required: true,
            message: '请选择星期几',
            trigger: 'change'
          },
          classTime: {
            validator: classTimeValidate,
            trigger: 'change',
            required: true
          },
          studentList: {
            required: true,
            validator: studentListValidate,
            trigger: 'change'
          }
        }
      };
    },
    computed: {
      visible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      selectedStudents: {
        handler(val) {
          if (!val?.length) return;
          const uniqueStudents = [...new Map(val.map((item) => [item.studentCode, item])).values()];
          this.form.curriculumId = val[0].curriculumId;
          this.form.courseContent = val[0].studyContent;
          this.form.studentList = uniqueStudents.map((i) => i.studentCode);
          this.form.gradeList = Array.from(new Set(uniqueStudents.filter((i) => i.grade && i.grade !== 0).map((i) => this.findGradeValue(i.grade))));
          this.form.deliverMerchantList = [...new Set(val.map((item) => item.deliverMerchant))];
        },
        immediate: true
      },
      'form.gradeList': {
        handler(newVal) {
          this.form.grade = newVal.join(',');
        },
        immediate: true
      }
    },
    methods: {
      //独立方法
      findGradeValue(grade) {
        const normalizedGrade = String(grade).trim();
        const item = this.gradeList.find((item) => item.label.includes(normalizedGrade) || item.value === normalizedGrade);
        return item?.value ?? normalizedGrade;
      },
      handleClose() {
        this.$emit('close');
        this.$refs.form.resetFields();
      },
      handleSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.submitForm();
          } else {
            this.$message.warning('请完善表单信息');
            return false;
          }
        });
      },

      async submitForm() {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        try {
          const uniqueStudents = [
            ...new Map(this.studentOptions.filter((item) => this.form.studentList.includes(item.studentCode)).map((item) => [item.studentCode, item])).values()
          ];
          const formData = {
            classTimeConfigId: this.form.classTimeConfigId,
            dayOfWeek: this.form.dayOfWeek,
            dayOfWeekName: this.weekOptions
              .filter((i) => this.form.dayOfWeek.includes(i.value))
              .map((i) => i.label)
              .join(','),
            lessonStartTime: this.form.startTime + ':00',
            lessonEndTime: this.form.endTime + ':00',
            curriculumId: this.form.curriculumId,
            grade: this.form.grade,
            type: this.form.type,
            courseContent: this.form.courseContent,
            deliverMerchant: this.form.deliverMerchantList,
            gradeName: this.gradeList
              .filter((i) => this.form.grade.split(',').some((ii) => ii == i.value))
              .map((i) => i.label)
              .join('、'),
            studentCode: uniqueStudents.map((item) => ({
              studentId: item.studentDeliverId,
              studentName: item.studentName,
              studentCode: item.studentCode,
              merchantCode: item.merchantCode,
              studentLeaveId: item.id,
              merchantName: item.merchantName
            }))
          };
          const res = await addTemporaryClass(formData);
          this.$message.success('提交成功');
          this.handleClose();
          this.$emit('submit');
        } catch (e) {
          console.log(e);
        } finally {
          loading.close();
        }
      },

      handleClassTimeConfigIdChange(obj) {
        if (Array.isArray(obj) && obj.length > 0) {
          const firstItem = obj[0];
          if (!firstItem.endTime || !firstItem.startTime || firstItem.usableWeek === undefined) {
            this.form.dayOfWeek = '';
            this.form.startTime = '';
            this.form.endTime = '';
            return;
          }
          this.form.dayOfWeek = obj.map((item) => item.usableWeek).join(',');
          this.form.startTime = firstItem.startTime;
          this.form.endTime = firstItem.endTime;
        }
        console.log(this.form, ' this.form');
      }
    }
  };
</script>
