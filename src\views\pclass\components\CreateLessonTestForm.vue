<template>
  <div>
    <el-dialog :visible.sync="createLessonStyle_" :width="screenWidth > 1300 ? '60%' : '90%'" title="填写试课单" :before-close="closeClass" :close-on-click-modal="false">
      <el-form ref="addForm" :rules="rules" :model="addForm" label-width="120px">
        <el-form-item label="课程类型" prop="curriculumName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="addForm.curriculumName" disabled />
        </el-form-item>
        <el-form-item label="试课人姓名" prop="expName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="addForm.expName" />
        </el-form-item>
        <el-form-item label="试课人手机号" prop="expPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="addForm.expPhone" />
        </el-form-item>
        <el-form-item label="学员年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-select v-model="addForm.grade" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学员性别" prop="sex" :inline="true" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="addForm.sex">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="期望课程时间" prop="expectTime" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <!-- <el-date-picker style="width: 100%" v-model="addForm.expectTime" type="datetime" placeholder="选择日期"
            format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
          </el-date-picker> -->
          <div style="display: flex">
            <el-date-picker v-model="expectTime1" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
            <el-time-select
              placeholder="选择时间"
              format="HH:mm:ss"
              v-model="expectTime2"
              :picker-options="{
                step: '00:30',
                start: '08:30',
                end: '23:30'
              }"
            >
              <!-- @change="changeTime" -->
            </el-time-select>
          </div>
        </el-form-item>
        <el-form-item label="学员所在区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-cascader v-model="addForm.address" :options="options02" @change="handleChange" />
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" :loading="submitLoading" @click="submitAdd('addForm')">完成</el-button>
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeClass">取消</el-button>
      </el-row>

      <el-dialog
        title=""
        :visible.sync="codeShow"
        width="15%"
        :before-close="codeDialogClose"
        append-to-body
        :close-on-click-modal="false"
        :show-close="false"
        class="codeDialog"
        @open="getQrCode"
      >
        <div class="image">
          <el-image style="width: 150px; height: 150px; margin-bottom: 20px" :src="qrCode" fit="fill"></el-image>
          <div class="color">
            <span>请让</span>
            <span style="color: red">家长</span>
            <span>扫一扫，添加上课小助理</span>
          </div>
          <div class="color">否则无法提交试课单</div>
        </div>
        <el-row :gutter="20" type="flex" justify="center">
          <el-button size="mini" style="width: 100px; border-radius: 10px" @click="codeDialogClose">取 消</el-button>
          <el-button size="mini" style="width: 100px; border-radius: 10px" type="primary" @click="downloadByBlob">保存二维码</el-button>
        </el-row>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import { fillInCourseZX, initQrCode, selAllExperienceUsableTime } from '@/api/studentClass/changeList';
  import { CodeToText } from 'element-china-area-data';
  export default {
    name: 'createLesson',
    //传值
    props: {
      allDelivers: '',
      createLessonStyle: {
        type: Boolean,
        default: false
      },
      // addForm: {},
      // rules: {},
      options: {},
      options02: {}
    },
    data() {
      var validateTime = (rule, value, callback) => {
        if (!this.expectTime1) {
          callback(new Error('请选择日期!'));
        } else if (!this.expectTime2) {
          callback(new Error('请选择时间!'));
        } else {
          callback();
        }
      };
      return {
        screenWidth: window.screen.width,
        ddress: '',
        qrCode: '',
        codeShow: false,
        timeList: [],
        expectTime1: '',
        expectTime2: '',
        addForm: {
          orderId: '',
          expName: '',
          expPhone: '',
          curriculumName: '',
          curriculumId: '',
          grade: '',
          sex: '',
          expectTime: '',
          province: '',
          city: '',
          area: '',
          address: [],
          referrerPhone: '',
          referrerName: ''
        },
        rules: {
          expName: [{ required: true, message: '请输入试课人姓名', trigger: 'blur' }],
          expPhone: [
            { required: true, message: '请输入试课人手机号', trigger: 'blur' },
            {
              validator: function (_, value, callback) {
                if (/^1[345789]\d{9}$/.test(value) == false) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          grade: [{ required: true, message: '请选择年级', trigger: 'blur' }],
          expectTime: [
            // { required: true, message: '请选择时间', trigger: 'change' }
            { validator: validateTime, trigger: 'blur' }
          ],
          sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
          referrerPhone: [{ required: true, message: '请填写手机号', trigger: 'blur' }],
          address: [{ required: true, message: '请选择区域', trigger: 'blur' }]
        },
        submitLoading: false,
        // 标志位，避免循环触发
        _isSettingAddress: false,
        _isUserSelectingAddress: false
      };
    },
    //计算属性
    computed: {
      createLessonStyle_: {
        get() {
          if (this.createLessonStyle) {
            this.submitLoading = false;
          }
          return this.createLessonStyle;
        },
        set(v) {
          console.log(v, 'v');
          this.$emit('changeDrawer', v);
        }
      }
    },
    watch: {
      // 监听 addForm 的变化，确保数据正确同步
      addForm: {
        handler(newVal, oldVal) {
          // 避免在地址选择过程中触发循环
          if (this._isUserSelectingAddress || this._isSettingAddress) {
            return;
          }

          // 避免循环触发：只在外部设置数据时处理，不在内部修改时处理
          if (newVal && typeof newVal === 'object' && Object.keys(newVal).length > 0) {
            // 检查是否是外部设置的数据（通过对比关键字段）
            const isExternalUpdate = !oldVal || newVal.orderId !== oldVal.orderId || newVal.expName !== oldVal.expName || newVal.expPhone !== oldVal.expPhone;

            // 只有在外部更新或初始设置时才处理地址
            if (isExternalUpdate) {
              console.log('addForm 外部数据变化:', newVal);

              // 如果有地址信息，解析并设置到级联选择器
              if (newVal.province && newVal.city && newVal.area && (!newVal.address || newVal.address.length === 0)) {
                // 使用标志位避免循环
                this._isSettingAddress = true;
                this.$nextTick(() => {
                  if (this._isSettingAddress && !this._isUserSelectingAddress) {
                    this.addForm.address = [newVal.province, newVal.city, newVal.area];
                    this._isSettingAddress = false;
                  }
                });
              }

              // 数据设置完成后，清除可能存在的验证错误
              this.$nextTick(() => {
                if (this.$refs['addForm']) {
                  this.$refs['addForm'].clearValidate();
                }
              });
            }
          }
        },
        deep: true,
        immediate: false
      },

      // 监听弹窗显示状态
      createLessonStyle: {
        handler(newVal) {
          if (!newVal) {
            // 弹窗关闭时重置数据
            this.resetFormData();
          }
        }
      }
    },
    created() {
      this.getQrCode();
      this.getConfigDetail();
    },
    mounted() {
      // 组件挂载后，确保表单验证被清除
      this.$nextTick(() => {
        this.clearFormValidation();
      });
    },
    methods: {
      // changeTime(e) {
      //   this.expectTime2 = e + ':00'
      // },
      async getConfigDetail() {
        const { data } = await selAllExperienceUsableTime();
        this.timeList = data.deliverUsableTimeConfigVoList;
      },
      // 获取二维码
      async getQrCode() {
        let data = {
          agentId: 1000004
        };
        const res = await initQrCode(data);
        // console.log(res)
        this.qrCode = res.data.url;
      },
      // 关闭弹框
      codeDialogClose() {
        this.codeShow = false;
      },
      // 下载图片
      downloadByBlob() {
        const src = `${this.qrCode}?t=${new Date().getTime()}`;
        fetch(src).then((res) => {
          res.blob().then((myBlob) => {
            const href = URL.createObjectURL(myBlob);
            const a = document.createElement('a');
            a.href = href;
            a.download = '二维码.jpg';
            a.click();
            a.remove();
          });
        });
        // let image = new Image()
        // image.setAttribute('crossOrigin', 'anonymous');
        // image.src = this.qrCode
        // let name = '二维码'
        // image.onload = () => {
        //   let canvas = document.createElement('canvas')
        //   canvas.width = image.width
        //   canvas.height = image.height
        //   let ctx = canvas.getContext('2d')
        //   ctx.drawImage(image, 0, 0, image.width, image.height)
        //   canvas.toBlob((blob) => {
        //     let url = URL.createObjectURL(blob)
        //     this.download(url, name)
        //     // 用完释放URL对象
        //     URL.revokeObjectURL(url)
        //   })
        // }
      },
      // 下载图片
      download(href, name) {
        let eleLink = document.createElement('a');
        eleLink.download = name;
        eleLink.href = href;
        eleLink.click();
        eleLink.remove();
      },
      // getStatus(val){
      //   debugger
      //   this.type = val;
      //   console.log(this.type)
      // },
      getStatus(val) {
        this.type = val;
        console.log(this.type);
      },

      // 提交
      async submitAdd(formName) {
        console.log(this.type);
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            this.submitAddSuccess();
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },

      submitAddSuccess() {
        this.addForm.expectTime = this.expectTime1 + ' ' + this.expectTime2;
        this.submitLoading = true;
        fillInCourseZX(this.addForm)
          .then((data) => {
            if (data.success) {
              this.$emit('refreshPage');
              this.$message({
                showClose: true,
                message: '提交成功',
                type: 'success'
              });
              this.closeClass();
            } else {
              this.submitLoading = false;
            }
          })
          .catch(() => {
            this.submitLoading = false;
          });
      },

      handleChange(value) {
        console.log(value, '地址选择变化');
        // 设置标志位，避免触发 watch 循环
        this._isUserSelectingAddress = true;
        this.getCodeToText(null, value);
        // 延迟重置标志位
        this.$nextTick(() => {
          this._isUserSelectingAddress = false;
        });
      },

      getCodeToText(codeStr, codeArray) {
        if (null === codeStr && null === codeArray) {
          return null;
        } else if (null === codeArray) {
          codeArray = codeStr.split(',');
        } else if (undefined === codeArray || !Array.isArray(codeArray)) {
          return null;
        }

        let area = [];
        switch (codeArray.length) {
          case 1:
            area.push(CodeToText[codeArray[0]]);
            break;
          case 2:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            break;
          case 3:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            area.push(CodeToText[codeArray[2]]);
            break;
          default:
            break;
        }

        // 只有在用户主动选择地址时才更新，避免循环
        if (this._isUserSelectingAddress || !this.addForm.province) {
          this.addForm.province = area[0] || '';
          this.addForm.city = area[1] || '';
          this.addForm.area = area[2] || '';
        }

        return area;
      },

      // 关闭弹窗
      closeClass() {
        this.submitLoading = false;

        // 立即重置表单数据，避免延迟导致的数据残留
        this.resetFormData();

        // 通知父组件关闭弹窗
        this.$parent.changeDrawer(false);
      },

      // 重置表单数据
      resetFormData() {
        // 先清除验证，避免重置时触发验证
        if (this.$refs['addForm']) {
          this.$refs['addForm'].clearValidate();
        }

        // 重置所有数据字段到初始状态
        this.addForm = {
          orderId: '',
          expName: '',
          expPhone: '',
          curriculumName: '',
          curriculumId: '',
          grade: '',
          sex: '',
          expectTime: '',
          province: '',
          city: '',
          area: '',
          address: [],
          referrerPhone: '',
          referrerName: ''
        };
        this.expectTime1 = '';
        this.expectTime2 = '';
        this.address = '';
        this.submitLoading = false;
        // 重置标志位
        this._isSettingAddress = false;
        this._isUserSelectingAddress = false;

        // 延迟清除表单字段，确保数据重置完成后再清除
        this.$nextTick(() => {
          if (this.$refs['addForm']) {
            this.$refs['addForm'].resetFields();
            this.$refs['addForm'].clearValidate();
          }
        });
      },

      // 清除表单验证
      clearFormValidation() {
        if (this.$refs['addForm']) {
          this.$refs['addForm'].clearValidate();
        }
      },

      // 初始化表单（弹窗打开时调用）
      initializeForm() {
        this.$nextTick(() => {
          this.clearFormValidation();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .borders {
    margin: 1vw 1vw;
    // width: 28vw;
    height: 28vw;
    border: 1px solid #cac8c8;
    border-radius: 20px;
  }

  .paike {
    margin-bottom: 20px;
    margin-left: 2vw;

    &:first-child {
      margin-top: 0.5vw;
    }
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
  }
  ::v-deep .el-drawer__header {
    color: #000;
    font-size: 22px;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
  }
  ::v-deep :focus {
    outline: 0;
  }
  ::v-deep .el-drawer__body {
    overflow-x: hidden;
    overflow-y: auto;
  }
  .image {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .color {
    color: #7a7a7a;
    margin-bottom: 6px;
  }
</style>
