<!-- 一对多-新班级列表-试课等待成班中 -->
<template>
  <div>
    <el-form :model="searchNum" ref="query" label-width="120px" class="container-card" size="small">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
        <el-col :span="6">
          <el-form-item label="学员名称:" prop="studentName">
            <el-input v-model="searchNum.studentName" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学员编号:" prop="studentCode">
            <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-select v-model="searchNum.curriculumId" class="full-width" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="searchNum.grade" size="small" class="full-width" placeholder="请选择" clearable>
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请假课程内容:" prop="studyContent">
            <el-input v-model="searchNum.studyContent" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="交付中心名称:" prop="deliverName">
            <el-input v-model="searchNum.deliverName" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="星期：" prop="dayOfWeek">
            <el-select v-model.trim="searchNum.dayOfWeek" filterable clearable placeholder="请选择">
              <el-option v-for="item in weeklist" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="时间：">
            <el-row class="classTime">
              <el-col :span="5">
                <el-time-select
                  placeholder="开始时间"
                  v-model="searchNum.lessonStartTime"
                  prefix-icon="''"
                  suffix-icon="''"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '24:00'
                  }"
                  style="width: 80px; border: none"
                ></el-time-select>
              </el-col>
              <el-col :span="5" style="text-align: right; margin: 0 10px 0 20px">至</el-col>
              <el-col :span="5">
                <el-time-select
                  placeholder="结束时间"
                  v-model="searchNum.lessonEndTime"
                  prefix-icon="''"
                  suffix-icon="''"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '23:30',
                    minTime: searchNum.lessonStartTime
                  }"
                  style="width: 80px; border: none"
                ></el-time-select>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="班级类型:" prop="expectClassType">
            <el-select v-model="searchNum.expectClassType" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in classTypes" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-row style="margin: 20px 0 20px 20px">
      <el-col style="margin-right: 20px" :span="1.5">
        <el-button type="primary" @click="headerList()">列表显示属性</el-button>
      </el-col>
      <el-col style="margin-right: 20px" :span="1.5">
        <el-button type="warning" @click="handleOpenAddClass">新增班级</el-button>
      </el-col>
    </el-row>
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      :header-cell-style="{ background: '#f5f7fa', 'text-align': 'center' }"
      :cell-style="{ 'text-align': 'center' }"
      @selection-change="handleSelectionChange"
      height="400"
      ref="multipleTable"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :min-width="getWidth(item.value)"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="showDetail(row)">补课单</el-button>
            <el-button type="warning" size="mini" @click="onAssign(row)">指派班级</el-button>
          </div>
          <!-- 课程类型 -->
          <div v-else-if="item.value == 'curriculumId'">
            <span>
              {{ getCourse(row.curriculumId) || '-' }}
            </span>
          </div>
          <!-- 年级 -->
          <div v-else-if="item.value == 'grade'">
            <span>
              {{ getGrade(row.grade) || '-' }}
            </span>
          </div>
          <!-- 上课时间 -->
          <div v-else-if="item.value == 'makeUpClassTime'">
            <el-row style="white-space: break-spaces">
              {{ row.makeUpClassTime || '-' }}
            </el-row>
          </div>
          <!-- 班级类型 -->
          <div v-else-if="item.value == 'expectClassTypeName'">
            {{ row.expectClassTypeName || '-' }}
          </div>
          <!-- 其他 -->
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row v-if="tableList && tableList.length > 0" type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination
        v-if="tableList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
    <!-- 新增班级 -->
    <CreateClassDialog
      v-model="dialogVisibleAdd"
      :course-list="courseList"
      :grade-list="gradeList"
      :student-options="addClassList"
      :selected-students="multipleSelection"
      :class-types="classTypes"
      @close="closeClassVisible"
      @submit="handleCreateClassSubmit"
    />
    <!--指派-->
    <AssignClassDialog
      v-model="assignDialog"
      :student-info="assignForm"
      :course-list="courseList"
      :grade-list="gradeList"
      @close="handleAssignClose"
      @success="handleAssignSuccess"
    />
    <!--    补课单-->
    <MakeUpClassDetailDialog v-model="showDetailDialog" :type="type" :gradeList="gradeList" :selected-students="multipleSelection" :detail-info="currentDetail" />
  </div>
</template>

<script>
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
import { getWaitingClassPageList } from '@/api/oneToManyClass/WaitingBuildingClass.js';
import { getOneToMoreClassList } from '@/api/oneToManyClass/newClassList';
import CreateClassDialog from './components/CreateClassDialog.vue';
import AssignClassDialog from './components/AssignClassDialog.vue';
import MakeUpClassDetailDialog from './components/MakeUpClassDetailDialog.vue';
import HeaderSettingsDialog from '../../../pclass/components/HeaderSettingsDialog.vue';
import trialDate from '@/views/pclass/components/trialDate';
import { getClassTypeEnum } from '@/api/oneToManyClass/classList'
export default {
  name: 'WaitingBuildingClassMissed',
  components: {
    HeaderSettingsDialog,
    trialDate,
    CreateClassDialog,
    AssignClassDialog,
    MakeUpClassDetailDialog
  },
  props: {
    gradeList: {
      type: Array,
      default: () => []
    },
    type: {
      //补课-派单中 '5' 补课-等待成班中'6'
      type: String,
      default: ''
    }
  },
  data() {
    return {
      classTypes: [], // 班级类型枚举列表
      courseList: [], // 课程类型
      timeout: null, // 用于防抖的定时器
      searchNum: {
        studentName: '', // 学生姓名
        studentCode: '', // 学生编号
        grade: '', //年级
        studyContent: '', // 请假课程内容
        curriculumId: '', // 课程类型
        deliverName: '', // 交付中心名称
        dayOfWeek: '',
        lessonStartTime: '',
        lessonEndTime: '',
        expectClassType: '', // 班级类型
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      total: 11, // 总数
      tableList: [], // 表格数据
      tableLoading: false, // 表格加载状态
      editFormId: '', //试课单id
      assignForm: { classId: '', studentName: '', studentCode: '', merchantCode: '', merchantName: '', studyContent: '' }, // 指派班级弹窗
      multipleSelection: [], // 选中的数据
      addClassList: [], // 创建班级的数据
      headerSettings: [
        { name: '学员姓名', value: 'studentName' },
        { name: '班级类型', value: 'expectClassTypeName' },
        { name: '学员编号', value: 'studentCode' },
        { name: '交付中心名称', value: 'deliverName' },
        { name: '上课时间', value: 'makeUpClassTime' },
        { name: '请假提交时间', value: 'leaveTime' },
        { name: '课程类型', value: 'curriculumId' },
        { name: '请假课程内容', value: 'studyContent' },
        { name: '年级', value: 'grade' },
        { name: '联系方式', value: 'phone' },
        { name: '操作', value: 'operate' }
      ],
      HeaderSettingsStyle: false, // 列表属性弹框
      tableHeaderList: [], // 获取表头数据
      dialogVisibleAdd: false, // 新增班级上课时间弹窗
      dialogAbutment: false, //补课单弹窗
      assignDialog: false, // 指派弹框
      showDetailDialog: false, //补课单弹框
      currentDetail: {},
      weeklist: [
        { value: 1, label: '周一' },
        { value: 2, label: '周二' },
        { value: 3, label: '周三' },
        { value: 4, label: '周四' },
        { value: 5, label: '周五' },
        { value: 6, label: '周六' },
        { value: 7, label: '周日' }
      ]
    };
  },
  created() {
    this.getCourseTypeList(); // 获取课程类型列表
    this.getHeaderList(); // 获取表头数据
    this.initData();
  },
  mounted() {
    this.getClassTypes(); // 获取班级类型列表
  },
  methods: {
    //获取课程类型列表
    async getCourseTypeList() {
      try {
        const res = await getOneToMoreClassList({});
        this.courseList = res.data;
      } catch (e) {
        console.log('获取课程类型列表失败', e);
      }
    },
    // 重置搜索框
    rest() {
      this.searchNum.lessonStartTime = '';
      this.searchNum.lessonEndTime = '';
      this.$refs.query.resetFields();
      this.initData01();
    },
    // 搜索
    initData01() {
      let startTimeStr = this.searchNum.lessonStartTime;
      let endTimeStr = this.searchNum.lessonEndTime;
      if (startTimeStr && !endTimeStr) {
        this.$message.error('请选择结束时间');
        return;
      }
      if (!startTimeStr && endTimeStr) {
        this.$message.error('请选择开始时间');
        return;
      }
      if (startTimeStr && endTimeStr) {
        if (startTimeStr >= endTimeStr) {
          this.$message.error('结束时间必须大于开始时间');
          return;
        }
      }
      (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
    },
    async initData() {
      this.tableLoading = true;
      try {
        let { data } = await getWaitingClassPageList(this.searchNum);
        if (!data) {
          this.tableList = [];
          this.total = 0;
          return;
        }
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.tableList = data.data;
        // 切换保持回显
        this.$nextTick(() => {
          this.changeStatusBtn();
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    // 选择开始时间
    changeTime(val) {
      if (!val) return;
      let arr = val.split(':');
      this.addForm.endTime = (arr[0] - -1 + '').padStart(2, '0') + ':' + arr[1];
    },
    // 新增班级
    handleOpenAddClass() {
      if (this.addClassList <= 0 && this.multipleSelection.length <= 0) {
        return this.$message.warning('请至少选择一个学员');
      }
      // 检查课程类型是否一致
      const isConflict = this.multipleSelection.some((i) => i.curriculumId !== this.multipleSelection[0].curriculumId);
      if (isConflict) {
        return this.$message.warning('所选班级课程类型不一致，请重新选择');
      }
      this.addUniqueData(this.addClassList, this.multipleSelection);
      this.dialogVisibleAdd = true;
    },
    closeClassVisible() {
      this.dialogVisibleAdd = false;
    },
    // 新增班级确定事件
    handleCreateClassSubmit() {
      this.initData();
      this.multipleSelection = []; // 重置选中数据
      this.addClassList = []; // 重置新增班级数据
    },
    // 指派班级
    onAssign(row) {
      this.assignForm = {
        ...row,
        courseName: this.getCourse(row.curriculumId),
        gradeLabel: this.getGrade(row.grade)
      };
      this.assignDialog = true;
    },
    // 指派成功
    handleAssignSuccess() {
      this.assignDialog = false;
      this.initData();
    },
    // 主页列表选中数据
    handleSelectionChange(val) {
      if (val.length > 0) {
        const firstItem = val[0];
        // 检查新增项是否满足条件
        const isInvalid = val.some((item) => item.deliverMerchant !== firstItem.deliverMerchant || item.curriculumId !== firstItem.curriculumId);
        if (isInvalid) {
          // 如果是批量选择
          if (val.length - this.multipleSelection.length > 1) {
            this.$refs.multipleTable.clearSelection();
            return this.$message.warning('所选学员必须来自同一交付中心且课程类型相同');
          }
          // 如果是单个选择
          else {
            const lastItem = val[val.length - 1];
            this.$refs.multipleTable.toggleRowSelection(lastItem, false);
            return this.$message.warning(`学员 ${lastItem.studentName} 的交付中心或课程类型与其他学员不一致`);
          }
        }
      }
      this.multipleSelection = val;
    },
    // 关闭指派弹框
    handleAssignClose() {
      this.assignDialog = false;
    },
    // 添加并排除重复数据
    addUniqueData(array, dataList) {
      if (dataList.length <= 0) return array;
      dataList.forEach((data) => {
        if (!array.some((i) => i.id === data.id)) {
          array.push(data);
        }
      });
    },
    // 切换保持回显 //multipleSelection
    changeStatusBtn() {
      let indexList = []; // 索引数组
      this.tableList.forEach((data) => {
        let index = this.addClassList.findIndex((i) => i.id === data.id);
        if (index !== -1) {
          this.$refs.multipleTable.toggleRowSelection(data, true);
          indexList.push(index);
        } else {
          this.$refs.multipleTable.toggleRowSelection(data, false);
        }
      });

      this.addClassList = this.addClassList.filter((i, ind) => !indexList.includes(ind));
    },
    // 分页
    handleSizeChange(val) {
      this.addUniqueData(this.addClassList, this.multipleSelection);
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.addUniqueData(this.addClassList, this.multipleSelection);
      this.searchNum.pageNum = val;
      this.initData();
    },
    // 点击补课单弹框
    showDetail(row) {
      this.currentDetail = {
        ...row,
        courseName: this.getCourse(row.curriculumId),
        gradeLabel: this.getGrade(row.grade)
      };
      this.showDetailDialog = true;
    },
    //拿到课程类型
    getCourse(val) {
      return this.courseList?.find((item) => item.id == val)?.enName || '-';
    },
    // 拿到年级
    getGrade(val) {
      return this.gradeList?.find((item) => item.value == val)?.label ?? '-';
    },
    // 获取宽度
    getWidth(val) {
      const widthMap = {
        makeUpClassTime: '300px',
        operate: '300px',
        dispatchOrderTime: '180px'
      };
      return widthMap[val] || '150px';
    },
    // 获取表头设置
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    // 接收子组件数据动态控制表头弹窗
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 接收子组件选择的表头数据
    selectedItems(arr) {
      if (arr) {
        let data = {
          type: 'WaitingBuildingClassMissed',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      }
    }, // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then((res) => {
        this.$message.success('操作成功');
        this.HeaderSettingsStyle = false;
        this.getHeaderList();
      });
    },
    // 获取表头设置
    async getHeaderList() {
      let data = {
        type: 'WaitingBuildingClassMissed'
      };
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          let tableHeaderList = JSON.parse(res.data.value);
          this.tableHeaderList = tableHeaderList.filter((item) => item);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      });
    },
    // 获取班级类型列表
    async getClassTypes() {
      const res = await getClassTypeEnum();
      this.classTypes = res.data;
    }
  }
};
</script>

<style lang="scss" scoped>
.classTime {
  border: 1px solid #ccd;
  border-radius: 4px;
  ::v-deep.el-input__inner {
    padding: 0 12px;
    border: none;
  }
  ::v-deep.el-input__suffix {
    top: 1px;
    right: 2px;
  }
}
</style>
