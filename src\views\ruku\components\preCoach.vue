<!--交付中心-入库管理-人员入库-教练的页面-->
<template>
  <div>
    <el-card v-if="zhuisfor">
      <el-form label-width="100px" label-position="right" class="frame" ref="zhuSou" :model="zhuSou">
        <!-- 1 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="8" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-input
                v-model="zhuSou.name"
                placeholder="请输入"
                size="small"
                style="width: 200px"
                @input="filterValue(zhuSou.name)"
                @change="changeTeacher(zhuSou.name)"
                @clear="clearSearchRecord"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别:" prop="sex">
              <el-select v-model="zhuSou.sex" placeholder="请选择" size="small" clearable style="width: 250px">
                <el-option label="男" value="1"></el-option>
                <el-option label="女" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="zhuSou.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 2 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="8">
            <el-form-item label="联系方式:" prop="mobile">
              <el-input v-model="zhuSou.mobile" placeholder="请选择" size="small" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所教学段:" prop="gradeStage">
              <el-select v-model="zhuSou.gradeStage" placeholder="请选择" size="small" clearable style="width: 250px">
                <el-option label="小学" value="1"></el-option>
                <el-option label="初中" value="2"></el-option>
                <el-option label="高中" value="3"></el-option>
                <el-option label="大学" value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所教模块:" prop="teachModule">
              <el-select v-model="zhuSou.teachModule" placeholder="请选择" size="small" clearable>
                <el-option label="单词" value="1"></el-option>
                <el-option label="语法" value="2"></el-option>
                <el-option label="阅读" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 3 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="8">
            <el-form-item label="岗位类型:" prop="postType">
              <el-select v-model="zhuSou.postType" placeholder="请选择" size="small" style="width: 200px" clearable>
                <el-option label="全职" value="1"></el-option>
                <el-option label="兼职" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isAdmin">
            <el-form-item label="交付中心编号:" prop="deliverMerchant">
              <el-input v-model.trim="zhuSou.deliverMerchant" placeholder="请输入" size="small" style="width: 200px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!isAdmin"></el-col>
          <el-col :span="8">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchBtn">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { searchTeacherApi } from '@/api/rukuManage/zhuTeacher';
import { selAllTeacher, findTeamList } from '@/api/studentClass/changeList';
import { bvstatusList } from '@/api/paikeManage/classCard';
import { getLeaderTeamList } from '@/api/orderManage';
import ls from '@/api/sessionStorage';

export default {
  name: 'zhujiaoTop',
  props: {
    zhuisfor: {
      // 定义接收的类型 还可以定义多种类型 [String, Undefined, Number]
      // 如果required为true,尽量type允许undefined类型，因为传递过来的参数是异步的。或者设置默认值。
      type: Boolean,
      // 定义是否必须传
      required: true
    }
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  data() {
    return {
      // 点谁谁true，默认都false
      xueisTo: false,
      zhuSou: {
        name: '',
        sex: '',
        gradeStage: '',
        mobile: '',
        teachModule: '',
        teachingType: '',
        postType: '',
        curriculumId: '',
        pageNum: 1,
        pageSize: 10
      },
      searchList: [],
      searchListzz: '',
      teachModulezz: '',
      isAdmin: false,

      option: [],
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        name: ''
      },
      courseList: [],
      leaderTeamList: []
    };
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
    console.log(this.isAdmin);
    this.getTeacherList();
  },
  mounted() {
    this.getbvstatusList();
    this.getTeamList();
  },
  methods: {
    async getTeamList() {
      let { data } = await findTeamList();
      // console.log(data);
      // console.log(data.data, '11111111111111111');
      this.leaderTeamList = [{ teamName: '无小组', id: '0' }, ...data.data];
    },
    getbvstatusList() {
      bvstatusList({}).then((res) => {
        this.courseList = res.data;
      });
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    // 获取教练
    async getTeacherList() {
      let allData = await selAllTeacher(this.selectObj);
      this.option = this.option.concat(allData.data.data);
    },

    filterValue(value) {
      console.log(value);
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = value;
      this.getTeacherList();
    },

    changeMessage() {
      this.$forceUpdate();
    },

    clearSearchRecord() {
      setTimeout(() => {
        if (this.zhuSou.name == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      }, 500);
      this.$forceUpdate();
    },
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
      }
    },
    async searchBtn() {
      // console.log("---------------2222")
      // this.zhuSou.teachModule = this.teachModulezz.toString()
      // this.zhuSou.gradeStage=this.searchListzz.toString();
      this.zhuSou.pageNum = 1;
      // let { data } = await searchTeacherApi(this.zhuSou)
      // this.searchList = data;
      // this.$emit('Searchlist', this.searchList)
      this.$emit('zhuSou', this.zhuSou);
    },

    // 切换分页
    async initData() {
      console.log('---------------3333');
      // this.zhuSou.teachModule = this.teachModulezz.toString()
      // this.zhuSou.gradeStage=this.searchListzz.toString();
      let { data } = await searchTeacherApi(this.zhuSou);
      this.searchList = data;
      this.$emit('Searchlist', this.searchList);
      this.$emit('zhuSou', this.zhuSou);
    },
    //重置
    rest() {
      this.$refs.zhuSou.resetFields();
      this.zhuSou = {
        name: '',
        sex: '',
        gradeStage: '',
        mobile: '',
        teachModule: '',
        teachingType: '',
        postType: '',
        curriculumId: '',
        pageNum: 1,
        pageSize: 10
      };
      this.searchBtn();
    }
  }
};
</script>

<style></style>
