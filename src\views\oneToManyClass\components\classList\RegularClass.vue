<!-- 交付中心-一对多学员管理-班级列表 -->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" ref="searchNum" :model="searchNum" :inline="true" style="display: flex; flex-wrap: wrap">
        <el-form-item label="班级名称:" prop="className" style="display: flex">
          <el-input v-model.trim="searchNum.className" clearable placeholder="请输入班级名称或编号"></el-input>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId" style="display: flex">
          <el-select v-model.trim="searchNum.curriculumId" filterable clearable placeholder="请选择">
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级人数上限:" prop="studentCount" style="display: flex">
          <el-input v-model.trim="searchNum.studentCount" type="number" clearable placeholder="请输入" @keydown.native="handleNumberInputE" @change="handleChangeNumber"></el-input>
        </el-form-item>
        <el-form-item v-if="isAdmin" label="交付中心编号:" prop="deliverMerchant" style="display: flex">
          <el-input v-model.trim="searchNum.deliverMerchant" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverName" style="display: flex">
          <el-input v-model.trim="searchNum.deliverName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="上课老师:" prop="teacherId" style="display: flex">
          <BaseElSelectLoadmore v-model="searchNum.teacherId" valueProp="id" labelProp="name" :searchFunc="getTeacherList"></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="分类:" prop="type" style="display: flex">
          <el-select v-model.trim="searchNum.type" clearable placeholder="请选择">
            <el-option v-for="item in courseTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学员名称:" prop="studentName" style="display: flex">
          <el-input v-model.trim="searchNum.studentName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="新增时间:" prop="searchTimeRange">
          <el-date-picker
            v-model="searchTimeRange"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 360px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="班级实际人数:" prop="studentRealCount" style="display: flex">
          <el-input v-model.trim="searchNum.studentRealCount" @input="changeInput" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="班级类型:" prop="classType" style="display: flex">
          <el-select v-model="searchNum.classType" filterable clearable placeholder="请选择">
            <el-option v-for="item in classTypes" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex; align-items: center; margin: 0 30px 18px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="search">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="reset">重置</el-button>
        </div>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <el-button type="warning" @click="handleClassAddClick()" style="margin: 20px 0 20px 20px">新增班级</el-button>

    <el-table
      v-loading="tableLoading"
      :data="classList"
      style="width: 100%"
      id="out-table"
      ref="configurationTable"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 300 : 160"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :show-overflow-tooltip="['classStudentText', 'gradeName', 'classTime'].includes(item.value)"
      >
        <template v-slot="{ row }" v-if="needTableSlotProp.includes(item.value)">
          <div v-if="item.value == 'type'">{{ commonFormat(courseTypeList, row.type) }}</div>
          <div v-else-if="item.value == 'classType'">{{ formatClassType(row.classType) }}</div>
          <div v-else-if="item.value == 'curriculumId'">{{ commonFormat(curriculumList, row.curriculumId) }}</div>
          <div v-else-if="item.value == 'showStudentCount'" style="position: relative">
            <span>{{ row.showStudentCount }}</span>
            <el-tag v-if="row.fullStrength" style="position: absolute; margin-left: 5px" type="danger" effect="dark">满</el-tag>
          </div>
          <div v-else-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="handleUpdateClick(row)">编辑</el-button>
            <el-button v-if="getIsShowCourseScheduling(row)" type="warning" size="mini" @click="handleCourseSchedulingClick(row)">排课</el-button>
            <el-button type="primary" size="mini" @click="handleOrderDispatchRecordClick(row.id)">派单记录</el-button>
            <el-button type="danger" size="mini" @click="handleRemoveClick(row.id)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑" :visible.sync="formDialogVisible" width="30%" :close-on-click-modal="false">
      <el-form :model="formData" :rules="formRules" label-width="120px" ref="formData" size="small" v-loading="formLoading">
        <el-form-item v-if="isAdmin" label="交付中心:" prop="deliverMerchant">
          <BaseElSelectLoadmore v-model="formData.deliverMerchant" valueProp="merchantCode" labelProp="merchantName" :searchFunc="getDeliverList" disabled></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId">
          <el-select v-model="formData.curriculumId" placeholder="请选择" disabled>
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级:" prop="grade">
          <el-select v-model="formData.grade" multiple placeholder="请选择" disabled>
            <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称:" prop="className">
          <el-input v-model.trim="formData.className" placeholder="请输入" disabled></el-input>
        </el-form-item>
        <el-form-item label="当前教练:" prop="currentTeacher">
          <el-input v-model.trim="formData.currentTeacher" placeholder="请输入" disabled></el-input>
        </el-form-item>
        <el-form-item label="分类:" prop="type">
          <el-radio-group v-model="formData.type" size="small" disabled>
            <el-radio v-for="item in courseTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学员:" prop="classStudentText">
          <el-input v-model.trim="formData.classStudentText" type="textarea" placeholder="请输入" disabled></el-input>
        </el-form-item>
        <el-form-item label="上课教练:" prop="teacherId">
          <el-select v-model="formData.teacherId" filterable placeholder="请选择" @change="handleTeacherChange(formData.teacherId, 'formData')" style="width: 100%">
            <el-option v-for="item in teacherList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="代课教练:" prop="secondTeacherId">
          <el-select v-model="formData.secondTeacherId" :disabled="!formData.teacherId" filterable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in secondTeacherList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right; margin-top: 40px">
          <el-button @click="handleUpdateCancelClick">取消</el-button>
          <el-button type="primary" @click="handleUpdateSubmitClick">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 新增班级弹窗 -->
    <el-dialog title="新增班级" :visible.sync="addClassDialogVisible" width="30%">
      <el-form :model="addClassFormData" :rules="addClassRules" label-width="110px" ref="addClassFormData" size="small">
        <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverMerchant">
          <BaseElSelectLoadmore
            v-model="addClassFormData.deliverMerchant"
            valueProp="merchantCode"
            labelProp="merchantName"
            :searchFunc="getDeliverList"
            @change="handleAddClassDeliverChange"
            style-data="width: 100%"
          ></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId">
          <el-select v-model="addClassFormData.curriculumId" placeholder="请选择" style="width: 100%" @change="handleAddClassCurriculumIdChange">
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级类型:" prop="classType">
          <el-select v-model="addClassFormData.classType" placeholder="请选择" :disabled="addClassFormData.type === '1'" style="width: 100%" @change="handleAddClassCurriculumIdChange">
            <el-option v-for="item in classTypes" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类:" prop="type">
          <el-radio-group v-model="addClassFormData.type" @change="handleAddClassCourseTypeChange">
            <el-radio v-for="item in courseTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="年级:" prop="grade">
          <el-select v-model="addClassFormData.grade" multiple placeholder="请选择" style="width: 100%">
            <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级人数上限:" prop="studentCount">
          <el-input-number class="add-class-student-count" v-model="addClassFormData.studentCount" :controls="false" :min="1" step-strictly placeholder="请输入"></el-input-number>
        </el-form-item>
        <el-form-item label="上课教练:" prop="teacherId">
          <el-select
            v-model="addClassFormData.teacherId"
            filterable
            placeholder="请选择"
            @change="handleTeacherChange(addClassFormData.teacherId, 'addClassFormData')"
            style="width: 100%"
          >
            <el-option v-for="item in teacherList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="代课教练:" prop="secondTeacherId">
          <el-select v-model="addClassFormData.secondTeacherId" :disabled="!addClassFormData.teacherId" filterable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in secondTeacherList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称:">
          <el-input placeholder="请填写以上信息，系统自动生成" disabled></el-input>
        </el-form-item>
        <el-form-item style="text-align: right; margin-top: 40px">
          <el-button @click="handleAddClassCancelClick">取消</el-button>
          <el-button type="primary" :loading="addClassLoading" @click="handleAddClassSubmitClick">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 排课抽屉弹窗 -->
    <el-drawer :visible.sync="courseSchedulingDialogVisible" size="40%" :append-to-body="true" :wrapperClosable="false" direction="rtl" center>
      <template #title>
        <span style="text-align: center; color: #000; font-weight: bold">排课</span>
      </template>
      <div class="course-scheduling" v-loading="courseSchedulingLoading">
        <div class="info">
          <div class="info-item">班级名称：{{ courseSchedulingFormData.className }}</div>
          <div class="info-item">班级编号：{{ courseSchedulingFormData.classCode }}</div>
        </div>
        <el-form
          :model="courseSchedulingFormData"
          :rules="courseSchedulingFormRules"
          class="form"
          label-position="left"
          label-width="90px"
          ref="courseSchedulingFormRef"
          size="small"
        >
          <el-form-item class="form-item" label="日期:" prop="dateList">
            <el-date-picker
              v-if="courseSchedulingFormData.type == '1'"
              v-model="courseSchedulingFormData.dateList"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="{ disabledDate: handlecourseSchedulingDialogDateDisable }"
              placeholder="选择日期"
              style="width: 73%"
            ></el-date-picker>
            <el-date-picker
              v-else
              v-model="courseSchedulingFormData.dateList"
              type="dates"
              value-format="yyyy-MM-dd"
              :picker-options="{ disabledDate: handlecourseSchedulingDialogDateDisable }"
              placeholder="选择日期"
              style="width: 73%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="form-item" label="时间:" prop="timeList">
            <BaseMultipleTimeRange
              :time-list="courseSchedulingFormData.timeList"
              start-time="startTime"
              time-num="timeHour"
              end-time="endTime"
              width="73%"
              :canAdd="courseSchedulingFormData.type == '2'"
              :inhibit-change="baseMultipleTimeRangeInhibitChange"
              @inhibitChange="$message.error('请先选择日期！')"
            ></BaseMultipleTimeRange>
          </el-form-item>
          <div class="parting-line" />
          <el-form-item class="form-item" label="上课老师:" prop="teacherId">
            <el-select v-model="courseSchedulingFormData.teacherId" placeholder="请选择" style="width: 73%">
              <el-option v-for="item in courseSchedulingTeachList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form-item" label="上课学员:" prop="classStudentList">
            <el-select v-model="courseSchedulingFormData.classStudentList" multiple placeholder="请选择" :disabled="courseSchedulingFormData.type == '1'" style="width: 73%">
              <el-option v-for="item in courseSchedulingStudentList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="courseSchedulingFormData.type == '1'" class="form-item">
            <div style="display: flex; align-items: baseline; width: 73%">
              <i class="el-icon-warning" style="color: #fbab16; margin-right: 6px"></i>
              <b>试课不可编辑学员，可通过「学员列表」选择学员转移至其它班级</b>
            </div>
          </el-form-item>
          <el-form-item class="form-item form-btnGroup">
            <el-button class="btn" @click="handleSchedulingCancelClick">取消</el-button>
            <el-button class="btn" type="primary" @click="handleSchedulingSubmitClick">确定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 历史派单记录弹窗 -->
    <ClasstHistoryOrderDispatchDialog :visible.sync="historyOrderDispatchDialogVisible" :classId="historyOrderDispatchClassId" />
  </div>
</template>

<script>
import HeaderSettingsDialog from '../../../pclass/components/HeaderSettingsDialog.vue';
import BaseElSelectLoadmore from '../studentList/BaseElSelectLoadmore.vue';
import ClasstHistoryOrderDispatchDialog from './ClasstHistoryOrderDispatchDialog.vue';
import {
  getAllOneToManyCurriculumType,
  getAllDeliver,
  getClassListData,
  getTeacherListByCurriculumIdAndMerchantCode,
  setClassUpdateData,
  setClassAddData,
  getCourseSchedulingTeacherList,
  setDeleteClassData,
  getCourseSchedulingDetail,
  setCourseSchedulingData,
  getClassTypeEnum
} from '@/api/oneToManyClass/classList';
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
import { GradeType, selAllTeacher } from '@/api/studentClass/changeList';
import BaseMultipleTimeRange from './BaseMultipleTimeRange.vue';
import ls from '@/api/sessionStorage';
import { log } from 'bpmn-js-token-simulation';
export default {
  name: 'RegularClass',
  props: {
    //regular常规班 makeup补课班级
    tabType: {
      type: String,
      default: ''
    }
  },
  components: {
    HeaderSettingsDialog,
    BaseElSelectLoadmore,
    BaseMultipleTimeRange,
    ClasstHistoryOrderDispatchDialog
  },
  data() {
    var validTimeList = (rule, value, callback) => {
      if (this.courseSchedulingLoading) {
        callback();
        return;
      }
      if (Array.isArray(this.courseSchedulingFormData.timeList)) {
        let isEmpty = true,
          isVacancy = false;
        this.courseSchedulingFormData.timeList.forEach((each) => {
          if ((!each.startTime || !each.endTime) && !isVacancy) {
            isVacancy = true;
          }
          if (each.startTime && each.endTime && isEmpty) {
            isEmpty = false;
          }
        });
        if (isEmpty) {
          callback(new Error('请选择时间段'));
        } else if (isVacancy) {
          callback(new Error('请补全时间段'));
        } else {
          callback();
        }
      } else {
        callback(new Error('类型错误'));
      }
    };
    var validDateList = (rule, value, callback) => {
      if (this.courseSchedulingLoading) {
        callback();
        return;
      }
      if (typeof this.courseSchedulingFormData.dateList == 'string') {
        if (!this.courseSchedulingFormData.dateList) {
          callback(new Error('请选择日期'));
          return;
        }
      } else {
        if (this.courseSchedulingFormData.dateList.length == 0) {
          callback(new Error('请选择日期'));
          return;
        }
      }
      callback();
    };
    return {
      isAdmin: false,
      isDeliveryCenter: false,
      deliveryCenterMerchantCode: '',
      deliveryCenterMerchantName: '',

      //搜索参数
      searchNum: {},
      // 新增时间
      searchTimeRange: [],
      //课程类型列表
      curriculumList: [],

      // 年级列表
      gradeList: [],
      courseTypeList: [
        { value: '1', label: '试课' },
        { value: '2', label: '正式课' }
      ],

      // 班级类型列表
      classTypes: [],

      // 获取表头数据
      tableHeaderList: [],

        // 列表属性弹框
        HeaderSettingsStyle: false,
        headerSettings: [
          { name: '班级编号', value: 'classCode' },
          { name: '班级名称', value: 'className' },
          { name: '班级类型', value: 'classType' },
          { name: '分类', value: 'type' },
          { name: '班级年级', value: 'gradeName' },
          { name: '课程类型', value: 'curriculumId' },
          { name: '班级实际人数/人数上限', value: 'showStudentCount' },
          { name: '班级成员', value: 'classStudentText' },
          { name: '上课老师', value: 'teacherName' },
          { name: '代课老师', value: 'secondTeacherName' },
          { name: '上课时间', value: 'classTime' },
          { name: '操作', value: 'operate' },
        ],

        tableLoading: false,
        needTableSlotProp: ['type', 'curriculumId', 'showStudentCount', 'operate', 'classType'],
        classList: [],

      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 编辑弹窗
      formDialogVisible: false,
      formLoading: false,
      formData: {},
      formRules: {
        type: { required: true, message: '请选择', trigger: 'change' },
        teacherId: { required: true, message: '请选择上课教练', trigger: 'change' }
      },
      deliverList: [],
      teacherList: [],
      secondTeacherList: [],

      // 新增班级弹窗
      addClassDialogVisible: false,
      addClassLoading: false,
      addClassFormData: {},
      addClassRules: {
        curriculumId: [{ required: true, message: '请选择课程类型', trigger: 'change' }],
        type: [{ required: true, message: '请选择分类', trigger: 'change' }],
        grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
        studentCount: [{ required: true, message: '请输入班级人数', trigger: 'blur' }],
        teacherId: [{ required: true, message: '请选择上课教练', trigger: 'change' }]
      },

      // 排课抽屉弹窗
      courseSchedulingDialogVisible: false,
      courseSchedulingLoading: false,
      courseSchedulingFormData: {},
      courseSchedulingTeachList: [],
      courseSchedulingStudentList: [],
      courseSchedulingFormRules: {
        dateList: [{ required: true, validator: validDateList, trigger: 'change' }],
        timeList: [{ required: true, validator: validTimeList, trigger: 'change' }],
        teacherId: [{ required: true, message: '请选择上课老师', trigger: 'change' }],
        classStudentList: [{ required: true, message: '请选择上课学员', trigger: 'change' }]
      },

      // 历史派单记录数据
      historyOrderDispatchDialogVisible: false,
      historyOrderDispatchClassId: '',
      inputTimer: null
    };
  },
  computed: {
    baseMultipleTimeRangeInhibitChange() {
      let dateList = this.courseSchedulingFormData.dateList;
      return !((typeof dateList == 'string' && dateList) || (dateList && dateList.length > 0));
    }
  },
  created() {
    // 是否是管理员(dxAdmin+187)
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';

    // 是否是交付中心
    this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';

    // 交付中心商户号
    let { merchantCode, merchantName } = JSON.parse(localStorage.getItem('sysUserInfo'));
    this.deliveryCenterMerchantCode = merchantCode;
    this.deliveryCenterMerchantName = merchantName;

    if (this.isAdmin) {
      this.headerSettings.splice(2, 0, {
        name: '交付中心名称',
        value: 'deliverName'
      });
      this.headerSettings.splice(3, 0, {
        name: '交付中心编号',
        value: 'deliverMerchant'
      });
      this.addClassRules.deliverMerchant = [{ required: true, message: '请选择交付中心', trigger: 'change' }];
    }

    // 获取表头设置
    this.getHeaderList();
  },
  mounted() {
    this.getClassTypes(); // 获取班级类型列表
    this.getCurriculumList();
    this.getGradeList();
    this.getClassList();
  },
  methods: {
    changeInput(val) {
      console.log('🚀 ~ changeInput ~ val:', typeof val);

      // 清除之前的定时器，确保只在最后一次输入后提示
      if (this.inputTimer) {
        clearTimeout(this.inputTimer);
      }

      if (typeof val !== 'string') return;

      const numericValue = val.replace(/\D/g, '');
      const hasNonNumeric = val !== numericValue;

      // 只在有非数字字符时才考虑提示
      if (hasNonNumeric) {
        // 设置新的定时器，延迟300ms显示提示，用户继续输入会清除此定时器
        this.inputTimer = setTimeout(() => {
          this.$message.warning('班级实际人数只能输入数字');
          this.searchNum.studentRealCount = ''; // 只保留数字部分
          this.inputTimer = null; // 重置定时器标识
        }, 300);
      } else {
        // 没有非数字字符时，清除可能存在的定时器
        this.inputTimer = null;
      }

      // 处理空值情况
      if (!numericValue) {
        this.searchNum.studentRealCount = '';
        return;
      }
    },
    // 搜索
    search() {
      this.pagination.pageNum = 1;
      this.getClassList();
    },
    // 重置
    reset() {
      this.searchNum = {};
      this.searchTimeRange = [];
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.getClassList();
    },
    // 查询栏-获取老师分页下拉
    getTeacherList(selectQuery) {
      return new Promise((resolve, reject) => {
        selAllTeacher(selectQuery)
          .then((res) => {
            resolve(
              res.data.data.map((item) => {
                return { value: item.value, label: item.label };
              })
            );
          })
          .catch((err) => reject(err));
      });
    },
    // 新增/编辑 弹窗 获取交付中心分页下拉
    getDeliverList(selectQuery) {
      return new Promise((resolve, reject) => {
        getAllDeliver(selectQuery)
          .then((res) => {
            resolve(
              res.data.data.map((item) => {
                return { value: item.merchantCode, label: item.merchantName };
              })
            );
          })
          .catch((err) => reject(err));
      });
    },
    // 获取班级列表
    getClassList() {
      this.tableLoading = true;
      if (this.searchTimeRange.length == 2) {
        this.searchNum.startTime = this.searchTimeRange[0];
        this.searchNum.endTime = this.searchTimeRange[1];
      }
      this.searchNum.pageNum = this.pagination.pageNum;
      this.searchNum.pageSize = this.pagination.pageSize;
      getClassListData(this.searchNum)
        .then((res) => {
          this.classList = res.data.data;
          this.classList.forEach((each) => {
            each.courseSchedulingStudentList = [];
            if (Array.isArray(each.deliverClassStudentVo) && each.deliverClassStudentVo.length > 0) {
              each.courseSchedulingStudentList = each.deliverClassStudentVo.map((item) => {
                return { label: `${item.studentName}（${item.studentCode}）`, value: item.studentCode, id: item.studentDeliverId, studentName: item.studentName };
              });
              each.classStudentText = each.courseSchedulingStudentList.map((item) => item.label).join('、');
            } else {
              each.classStudentText = '-';
            }
          });
          this.pagination.total = Number(res.data.totalItems);
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    // 获取课程类型下拉列表
    getCurriculumList() {
      getAllOneToManyCurriculumType().then((res) => {
        this.curriculumList = res.data.map((item) => {
          return { label: item.enName, value: item.id };
        });
      });
    },
    // 获取年级列表
    getGradeList() {
      GradeType().then((res) => {
        this.gradeList = res.data.map((item) => {
          return { value: item.value, label: item.label };
        });
      });
    },
    // 打开列表属性弹窗
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    // 同步列表属性弹窗开启状态
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 接收子组件选择的表头数据
    selectedItems(arr) {
      if (arr) {
        let data = {
          type: 'RegularClass',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      }
    },
    // 获取表头设置
    async getHeaderList() {
      let data = {
        type: 'RegularClass'
      };
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          this.tableHeaderList = [];
          JSON.parse(res.data.value).forEach((item) => {
            if (item) {
              this.tableHeaderList.push(item);
            }
          });
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      });
    },
    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(() => {
        this.$message.success('操作成功');
        this.HeaderSettingsStyle = false;
        this.getHeaderList();
      });
    },
    // 动态class
    getRowClass({ rowIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa';
      }
    },
    // 更改每页条数
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.getClassList();
    },
    //更改当前页
    handleCurrentChange(val) {
      this.pagination.pageNum = val;
      this.getClassList();
    },
    // 编辑
    handleUpdateClick(row) {
      this.formData = { ...row };
      this.formData.type = String(this.formData.type);
      this.formData.curriculumId = String(this.formData.curriculumId);
      this.formData.grade = this.formData.grade ? this.formData.grade.split(',') : [];
      this.formData.currentTeacher = this.formData.teacherName;
      this.getEditTeacherListByCurriculumIdAndMerchantCode(this.formData.curriculumId, this.formData.type, this.formData.deliverMerchant).then(() => {
        this.formLoading = false;
      });
      this.formLoading = true;
      this.formDialogVisible = true;
    },
    // 通过课程大类及交付中心商户号获取上课教练及代课教练数据
    getEditTeacherListByCurriculumIdAndMerchantCode(curriculumId, type, merchantCode) {
      return new Promise((resolve) => {
        getTeacherListByCurriculumIdAndMerchantCode(curriculumId, merchantCode, type).then((res) => {
          this.teacherList = res.data.map((item) => {
            return { label: item.teacherName, value: item.teacherId, teamId: item.teamId };
          });
          this.getSecondTeacherListByTeamId(this.teacherList, this.formData.teacherId);
          // 查看代课教练是否存在于上课教练所在的小组列表中
          let isExistence = this.secondTeacherList.some((item) => item.value == this.formData.secondTeacherId);
          if (!isExistence) {
            // 不存在则清空代课教练
            this.formData.secondTeacherId = '';
          }
          resolve();
        });
      });
    },
    // 提交编辑
    handleUpdateSubmitClick() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          const submitForm = {
            id: this.formData.id,
            teacherId: this.formData.teacherId,
            teacherName: '',
            secondTeacherId: this.formData.secondTeacherId,
            secondTeacherName: ''
          };
          if (this.formData.teacherId) {
            submitForm.teacherName = this.commonFormat(this.teacherList, this.formData.teacherId);
          }
          if (this.formData.secondTeacherId) {
            submitForm.secondTeacherName = this.commonFormat(this.teacherList, this.formData.secondTeacherId);
          }
          setClassUpdateData(submitForm).then(() => {
            this.$message.success('编辑成功');
            this.formDialogVisible = false;
            this.getClassList();
          });
        }
      });
    },
    // 取消编辑
    handleUpdateCancelClick() {
      this.$message.info('取消编辑');
      this.formDialogVisible = false;
      this.formData = {};
    },
    // 新增班级
    handleClassAddClick() {
      if (this.isAdmin) {
        this.addClassFormData = { grade: [], teacherId: '' };
      } else if (this.isDeliveryCenter) {
        this.addClassFormData = { grade: [], teacherId: '', deliverMerchant: this.deliveryCenterMerchantCode, deliverName: this.deliveryCenterMerchantName };
      }
      this.teacherList = [];
      this.$nextTick(() => {
        this.$refs.addClassFormData?.clearValidate();
        this.addClassDialogVisible = true;
      });
    },
    // 处理新增班级弹窗-交付中心改变事件
    handleAddClassDeliverChange(deliverMerchant, deliverName) {
      this.$set(this.addClassFormData, 'deliverName', deliverName);
      let { curriculumId, type } = this.addClassFormData;
      if (curriculumId && type) {
        this.getAddTeacherListByCurriculumIdAndMerchantCode(curriculumId, deliverMerchant, type);
      }
    },
    // 处理新增班级弹窗-课程大类改变事件
    handleAddClassCurriculumIdChange(curriculumId) {
      let { deliverMerchant, type } = this.addClassFormData;
      if (deliverMerchant && type) {
        this.getAddTeacherListByCurriculumIdAndMerchantCode(curriculumId, deliverMerchant, type);
      }
    },
    // 处理新增班级弹窗-分类改变事件
    handleAddClassCourseTypeChange(courseType) {
      let { curriculumId, deliverMerchant } = this.addClassFormData;
      if (curriculumId && deliverMerchant) {
        this.getAddTeacherListByCurriculumIdAndMerchantCode(curriculumId, deliverMerchant, courseType);
      }
    },
    // 处理新增/编辑班级弹窗-上课老师改变事件
    handleTeacherChange(teacherId, prop) {
      if (!teacherId) return;
      this[prop].secondTeacherId = '';
      this.getSecondTeacherListByTeamId(this.teacherList, teacherId);
    },
    // 通过teacherId获取teacherList中同teamId的secondTeacherList
    getSecondTeacherListByTeamId(teacherList, teacherId) {
      const teamId = teacherList.find((teacher) => teacher.value === teacherId)?.teamId;
      if (teamId) {
        this.secondTeacherList = teacherList.filter((item) => item.teamId == teamId && item.value != teacherId);
      } else {
        this.secondTeacherList = [];
      }
    },
    // 获取添加教师列表通过课程id和商家代码
    getAddTeacherListByCurriculumIdAndMerchantCode(curriculumId, deliverMerchant, courseType) {
      getTeacherListByCurriculumIdAndMerchantCode(curriculumId, deliverMerchant, courseType).then((res) => {
        this.$set(this.addClassFormData, 'teacherId', '');
        this.$set(this.addClassFormData, 'secondTeacherId', '');
        this.teacherList = res.data.map((item) => {
          return { label: item.teacherName, value: item.teacherId, teamId: item.teamId };
        });
      });
    },
    // 提交新增班级
    handleAddClassSubmitClick() {
      this.$refs.addClassFormData.validate((valid) => {
        if (valid) {
          this.addClassLoading = true;
          const submitForm = { ...this.addClassFormData };
          if(submitForm.type === '1') {
            delete submitForm.classType;
          }
          if (submitForm.teacherId) {
            submitForm.teacherName = this.commonFormat(this.teacherList, submitForm.teacherId);
          }
          if (submitForm.secondTeacherId) {
            submitForm.secondTeacherName = this.commonFormat(this.teacherList, submitForm.secondTeacherId);
          }
          let gradeName = '';
          submitForm.grade.forEach((item) => {
            gradeName += this.commonFormat(this.gradeList, item) + '、';
          });
          submitForm.gradeName = gradeName && gradeName.substring(0, gradeName.length - 1);
          submitForm.grade = submitForm.grade.join(',');
          console.log('this.addClassFormData:', submitForm);
          setClassAddData(submitForm)
            .then(() => {
              this.$message.success('新增班级成功');
              this.addClassLoading = false;
              this.addClassDialogVisible = false;
              this.getClassList();
            })
            .catch(() => {
              this.addClassLoading = false;
            });
        }
      });
    },
    // 取消新增班级
    handleAddClassCancelClick() {
      this.$message.info('取消新增班级');
      this.addClassDialogVisible = false;
      setTimeout(() => {
        this.addClassFormData = { grade: [] };
      }, 100);
    },
    getIsShowCourseScheduling(row) {
      // 如果不是交付中心，直接不显示排课按钮
      if (!this.isDeliveryCenter) {
        return false;
      }
      // 判断排课是否可见
      if (row.type == 1) {
        // 类型为1:试课时，仅当未开始课程时显示排课按钮
        return row.isStartCourse == 0;
      } else {
        // 其他类型默认显示排课按钮
        return true;
      }
    },
    // 处理排课点击事件
    handleCourseSchedulingClick(row) {
      this.courseSchedulingStudentList = row.courseSchedulingStudentList;
      if (this.courseSchedulingStudentList.length == 0) {
        this.$message.warning('该班级不存在学员，无法排课');
        return;
      }
      this.courseSchedulingFormData = {
        type: row.type,
        id: row.id,
        curriculumId: row.curriculumId,
        className: row.className,
        classCode: row.classCode,
        dateList: row.type == 1 ? '' : [],
        timeList: [{ startTime: '', endTime: '', timeHour: 1 }],
        teacherId: row.teacherId,
        classStudentList: this.courseSchedulingStudentList.map((item) => item.value)
      };
      this.$refs.courseSchedulingFormRef?.clearValidate();
      this.courseSchedulingLoading = true;
      this.courseSchedulingDialogVisible = true;
      getCourseSchedulingTeacherList(row.id).then((res) => {
        let courseSchedulingTeachList = res.data.map((item) => {
          return { label: item.name, value: item.id };
        });
        this.courseSchedulingTeachList = this.handleObjectArrayDeduplication(courseSchedulingTeachList, 'value');
        if (this.courseSchedulingFormData.type == 1) {
          getCourseSchedulingDetail(row.id)
            .then((res) => {
              if (res.data) {
                this.courseSchedulingFormData.dateList = res.data.studyDate;
                const startTime = res.data.startStudyTime.substring(11, 16);
                const endTime = res.data.endStudyTime.substring(11, 16);
                if (startTime && endTime) {
                  const startHour = startTime.substring(0, 2);
                  const endHour = endTime.substring(0, 2);
                  const timeItem = { startTime, endTime, timeHour: endHour - startHour };
                  this.courseSchedulingFormData.timeList = [timeItem];
                }
              }
              this.courseSchedulingLoading = false;
            })
            .catch(() => {
              this.courseSchedulingLoading = false;
            });
        } else {
          this.courseSchedulingLoading = false;
        }
      });
    },
    // 处理对象数据去重
    handleObjectArrayDeduplication(arr, prop = 'id') {
      const result = arr
        .reduce((acc, cur) => {
          const key = cur[prop]; // 按 id 去重
          if (!acc.has(key)) {
            acc.set(key, cur);
          }
          return acc;
        }, new Map())
        .values();
      return [...result];
    },
    // 提交排课
    handleSchedulingSubmitClick() {
      this.$refs.courseSchedulingFormRef.validate((valid) => {
        if (valid) {
          console.log('handleSchedulingSubmitClick this.courseSchedulingFormData:', JSON.stringify(this.courseSchedulingFormData));
          const submitForm = {};
          submitForm.deliverClassId = this.courseSchedulingFormData.id;
          submitForm.curriculumId = this.courseSchedulingFormData.curriculumId;
          submitForm.teacherId = this.courseSchedulingFormData.teacherId;
          submitForm.studentDeliverDtoList = this.courseSchedulingFormData.classStudentList.map((studentCode) => {
            let item = this.courseSchedulingStudentList.find((i) => i.value == studentCode);
            return {
              studentCode: item.value,
              studentName: item.studentName,
              id: item.id
            };
          });
          if (this.courseSchedulingFormData.type == 1) {
            submitForm.date = this.courseSchedulingFormData.dateList;
            const timeItem = this.courseSchedulingFormData.timeList[0];
            submitForm.time = `${timeItem.startTime},${timeItem.endTime}`;
          } else {
            submitForm.dateList = this.courseSchedulingFormData.dateList;
            submitForm.timeList = this.courseSchedulingFormData.timeList;
          }
          console.log('handleSchedulingSubmitClick submitForm:', JSON.stringify(submitForm));
          this.courseSchedulingLoading = true;
          setCourseSchedulingData(submitForm, this.courseSchedulingFormData.type)
            .then(() => {
              this.$message.success('排课成功');
              this.courseSchedulingDialogVisible = false;
              this.courseSchedulingLoading = false;
              this.courseSchedulingFormData = {};
            })
            .catch(() => {
              this.courseSchedulingLoading = false;
            });
        }
      });
    },
    // 取消排课
    handleSchedulingCancelClick() {
      this.$message.info('取消排课');
      this.courseSchedulingDialogVisible = false;
      setTimeout(() => {
        this.courseSchedulingLoading = false;
        let type = this.courseSchedulingFormData.type;
        this.courseSchedulingFormData = {
          dateList: type == 1 ? '' : [],
          timeList: [{ startTime: '', endTime: '', timeHour: 1 }],
          classStudentList: []
        };
      }, 100);
    },
    // 处理派单记录点击事件
    handleOrderDispatchRecordClick(id) {
      this.historyOrderDispatchClassId = id;
      this.historyOrderDispatchDialogVisible = true;
    },
    // 删除
    handleRemoveClick(id) {
      this.$confirm('确定删除该班级？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          setDeleteClassData(id)
            .then(() => {
              loading.close();

                this.$message.success('删除成功');
                this.getClassList();
              })
              .catch(() => {
                loading.close();
              });
          })
          .catch(() => {
            this.$message.info('已取消删除');
          });
      },
      // 排课日期禁用
      handlecourseSchedulingDialogDateDisable(time) {
        return time.getTime() < Date.now() - 3600 * 1000 * 24;
      },
      commonFormat(array, key) {
        for (let item of array) {
          if (item.value == key) {
            return item.label;
          }
        }
        return '无';
      },
      handleNumberInputE(event) {
        if (event.key == 'e' || event.key == 'E') {
          event.returnValue = false;
          return false;
        }
        return true;
      },
      // 数字输入框输入时，不允许输入非数字
      handleChangeNumber() {
        if (this.searchNum.studentCount) {
          let newValue = '';
          if (/[^\d]/g.test(this.searchNum.studentCount)) {
            newValue = this.searchNum.studentCount.replaceAll(/[^\d]/g, '');
          }
          newValue && this.$set(this.searchNum, 'studentCount', newValue);
        }
      },
      formatClassType(code) {
        if (code === undefined || code === null) return '-';
        const classType = this.classTypes.find(item => String(item.code) === String(code));
        return classType ? classType.name : '-';
      },
      async getClassTypes() {
        const res = await getClassTypeEnum();
        this.classTypes = res.data;
      }
    }
  };
</script>

<style lang="scss" scoped>
.class_tab {
  padding: 0 0 20px 25px;
}
.add-class-student-count {
  width: 100%;
  &::v-deep.el-input-number.is-without-controls .el-input__inner {
    text-align: left;
  }
}
.course-scheduling {
  .info {
    border-bottom: 1px solid #e5e5e5;
    padding: 0 20px 10px;
    color: #666;
    font-weight: bold;
    .info-item {
      padding-bottom: 20px;
    }
  }
  .form {
    padding: 30px 0 10px;
    .form-item {
      margin-left: 20px;
    }
    .form-btnGroup {
      text-align: center;
      margin-top: 40px;
      .btn {
        padding-left: 30px;
        padding-right: 30px;
        margin: 0 15px;
      }
    }
    .parting-line {
      width: 100%;
      margin-bottom: 20px;
      border-bottom: 1px solid #e5e5e5;
    }
  }
}
</style>
