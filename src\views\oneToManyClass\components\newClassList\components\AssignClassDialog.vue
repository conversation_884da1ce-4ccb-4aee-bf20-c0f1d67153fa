<template>
  <el-dialog :visible.sync="visible" width="600px" :title="'指派班级'" destroy-on-close append-to-body :before-close="handleClose">
    <el-form ref="form" label-position="right" v-loading="submitting" label-width="95px" style="padding-right: 20px">
      <el-form-item label="姓名：">
        <el-input v-model="form.studentName" class="full-width" disabled></el-input>
      </el-form-item>
      <el-form-item label="学员编号：">
        <el-input v-model="form.studentCode" class="full-width" disabled></el-input>
      </el-form-item>
      <el-form-item label="课程类型：">
        <el-input v-model="form.courseName" class="full-width" disabled></el-input>
      </el-form-item>
      <el-form-item label="年级：">
        <el-input :value="form.gradeLabel" class="full-width" disabled></el-input>
      </el-form-item>
      <el-form-item label="交付中心：">
        <el-input :value="form.deliverName" class="full-width" disabled></el-input>
      </el-form-item>
      <el-form-item label="班级名称：">
        <el-select class="full-width" placeholder="请选择" :loading="classLoading" v-model="form.classId" filterable>
          <el-option v-for="item in classOptions" :key="item.classCode" :label="item.className" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { getTemporaryClassListByMerchantCode, assignClasses } from '@/api/oneToManyClass/newClassList';

  export default {
    name: 'AssignClassDialog',
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      studentInfo: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        form: {
          classId: '', //新班级id
          oldClassId: '', //旧班级id
          studentName: '', // 姓名
          studentCode: '', //学员编号
          studentLeaveId: '', // 请假id 取class_student_leave的id
          merchantCode: '', // merchantCode
          merchantName: '', // 门店名称
          curriculumId: '', // 课程大类
          deliverMerchant: '', //交付中心编号
          deliverName: '', // 交付中新编号
          gradeLabel: '', // 年级名称
          courseName: '' // 课程名称
        },
        classOptions: [],
        loading: false,
        classLoading: false,
        submitting: false
      };
    },
    computed: {
      visible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      studentInfo: {
        handler(val) {
          if (val) {
            this.form = {
              ...this.form,
              ...val,
              courseName: val.courseName,
              gradeLabel: val.gradeLabel,
              studentDeliverId: val.studentDeliverId, // 学生id
              studentLeaveId: val.id,
              studentName: val.studentName,
              deliverMerchant: val.deliverMerchant,
              deliverName: val.deliverName,
              studyContent: val.studyContent
            };
            this.form.deliverMerchant && this.fetchClasses(this.form.deliverMerchant);
          }
        },
        immediate: true
      }
    },
    methods: {
      handleClose() {
        this.$emit('close');
        this.resetForm();
      },
      // 获取班级数据源
      async fetchClasses(deliverMerchant) {
        this.classLoading = true;
        try {
          const res = await getTemporaryClassListByMerchantCode({ deliverMerchant });
          this.classOptions = res.data;
          const classExists = res.data.some((item) => item.id === this.form.classId);
          if (!classExists) {
            this.form.classId = ''; // 如果不存在，清空classId
          }
        } catch (e) {
        } finally {
          this.classLoading = false;
        }
      },
      async handleSubmit() {
        this.submitting = true;
        try {
          const data = {
            classId: this.form.classId,
            oldClassId: this.form.oldClassId,
            studentName: this.form.studentName,
            studentLeaveId: this.form.studentLeaveId,
            studentCode: this.form.studentCode,
            merchantCode: this.form.merchantCode,
            merchantName: this.form.merchantName,
            curriculumId: this.form.curriculumId,
            deliverMerchant: this.form.deliverMerchant,
            studentDeliverId: this.form.studentDeliverId,
            classContent: this.form.studyContent
          };
          const res = await assignClasses(data);
          if (res.code == 20000) {
            this.$message.success('指派成功');
            this.$emit('success');
          }
        } catch (e) {
        } finally {
          this.handleClose();
          this.submitting = false;
        }
      },

      resetForm() {
        this.form = {
          classId: '',
          name: '',
          studentCode: '',
          courseName: '',
          grade: '',
          deliverName: '',
          curriculumId: '',
          merchantCode: '',
          merchantName: ''
        };
        this.classOptions = [];
      }
    }
  };
</script>

<style scoped lang="scss"></style>
