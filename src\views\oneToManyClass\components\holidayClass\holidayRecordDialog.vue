<template>
  <div>
    <el-dialog title="请假记录" width="920px" :visible.sync="dialogVisible" destroy-on-close append-to-body>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        height="320px"
        :header-cell-style="{ background: '#f5f7fa' }"
        :cell-style="{ 'text-align': 'center' }"
        style="width: 100%"
      >
        <el-table-column prop="leaveTime" label="请假发起时间" min-width="180px" align="center"></el-table-column>
        <el-table-column prop="courseTime" label="请假课程时间" width="250px" align="center"></el-table-column>
        <el-table-column prop="studyContent" label="课程内容" width="200px" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="courseStatus" label="课程状态" width="120px" align="center">
          <template v-slot="{ row }">{{ row.courseStatus === 1 ? '已删除' : '未删除' }}</template>
        </el-table-column>
        <el-table-column prop="source" label="请假发起方式" width="120px" align="center">
          <template v-slot="{ row }">{{ row.source === 1 ? '家长发起' : '后台发起 ' }}</template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import { getStudentLeaveList } from '@/api/oneToManyClass/studySchedule';

  export default {
    name: 'HolidayRecordDialog',
    components: {},
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      holidayRecordRow: {
        type: Object,
        default: () => ({})
      },
      courseType: {
        //1试课 2正课
        type: String,
        default: ''
      }
    },
    data() {
      return {
        tableLoading: false,
        formData: {
          studentCode: '', // 学生code
          classId: null //classId
        },
        tableData: []
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      dialogVisible(val) {
        if (val) {
          const { studentCode, classId } = this.holidayRecordRow;
          this.formData.studentCode = studentCode;
          this.formData.classId = classId;
          this.formData.studentCode && this.formData.classId && this.getRecordList();
        }
      }
    },
    created() {},
    mounted() {},
    methods: {
      async getRecordList() {
        this.tableLoading = true;
        try {
          const res = await getStudentLeaveList({ ...this.formData });
          this.tableData = res.data;
        } catch (e) {
          console.log(e);
        } finally {
          this.tableLoading = false;
        }
      },
      onSubmit() {
        this.$emit('submit');
        this.dialogVisible = false;
      }
    }
  };
</script>
