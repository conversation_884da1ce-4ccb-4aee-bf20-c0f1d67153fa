<template>
  <div class="dx-fixed-footer" ref="footer">
    <div class="dx-fixed-footer-inner" :style="{ width: width + 'px' }">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DxFixedFooterButton',
    props: {},
    data() {
      return {
        width: 100
      };
    },
    mounted() {
      window.addEventListener('resize', this.resizeHandler);
      this.resizeHandler();
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resizeHandler);
    },
    methods: {
      resizeHandler() {
        this.width = this.$refs.footer.clientWidth;
      }
    }
  };
</script>

<style lang="scss">
  .dx-fixed-footer {
    flex-shrink: 0;
  }
  .dx-fixed-footer-inner {
    position: fixed;
    bottom: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 72px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(144, 157, 182, 0.3);
  }
</style>
