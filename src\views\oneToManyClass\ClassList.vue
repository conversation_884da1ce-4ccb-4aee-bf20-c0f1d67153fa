<!-- 交付中心-一对多学员管理-班级列表 -->
<template>
  <div>
    <el-radio-group v-model="activeTab" class="class_tab" size="medium" @change="handleTabsClick">
      <el-radio-button label="regular">常规班</el-radio-button>
      <el-radio-button label="makeup">补课班</el-radio-button>
    </el-radio-group>
    <!-- 常规班-->
    <RegularClass v-if="activeTab === 'regular'" :tabType="activeTab" />
    <MakeupClass v-if="activeTab === 'makeup'" :tabType="activeTab" />
  </div>
</template>

<script>
  import RegularClass from '@/views/oneToManyClass/components/classList/RegularClass.vue';
  import MakeupClass from '@/views/oneToManyClass/components/classList/MakeupClass.vue';

  export default {
    name: 'ClassList',
    components: { MakeupClass, RegularClass },
    data() {
      return {
        activeTab: 'regular'
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      handleTabsClick(e) {
        this.activeTab = e;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .class_tab {
    padding: 20px 0 20px 25px;
  }
</style>
