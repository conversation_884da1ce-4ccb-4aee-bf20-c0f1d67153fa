/**
 * 表格合并
 * feedbackTableData：[
 *   // feedbackTableData 里的每一个对象表示每一张表格
 *   {
 *     label: '表格标题',
 *     list: [ // 表格数据
 *       // list 里的每一个对象表示每一行
 *       {
 *         label: '维度内容' ,// 上下一样的label合并
 *         name: '观察要点内容' ,
 *         selectKey: '', // 记录符号（ √ / × / 无 ）对应的key
 *         notes: '' // 对应备注字段/ 空代表不需要字段
 *         notesPlaceholder: '' // 对应备注字段提示
 *       }
 *     ]
 *   }
 * ]
 */
export default [
  {
    "label": "课堂实时监测",
    "list": [
      {
        "label": "学习准备",
        "name": "1.准备预习任务完成",
        "selectKey": "preparationCompleted",
        "notes": ""
      },
      {
        "label": "学习准备",
        "name": "2.进门测试题目正确率",
        "selectKey": "entryTestAccuracy",
        "notes": "entryTestRate",
        "notesPlaceholder": "请输入正确率"
      },
      {
        "label": "注意力集中",
        "name": "1.眼神跟随教师/屏幕",
        "selectKey": "eyeFollowingScore",
        "notes": ""
      },
      {
        "label": "注意力集中",
        "name": "2.无关操作次数<=2次/节课",
        "selectKey": "irrelevantActions",
        "notes": ""
      },
      {
        "label": "课堂参与度",
        "name": "1.主动回答问题>=1次/节课",
        "selectKey": "activeAnswers",
        "notes": ""
      },
      {
        "label": "课堂参与度",
        "name": "2.互动次数和质量",
        "selectKey": "interactionCount",
        "notes": ""
      }
    ]
  },
  {
    "label": "教学效果观察",
    "list": [
      {
        "label": "知识掌握",
        "name": "1.课后留题正确率>=70%",
        "selectKey": "postClassAccuracy",
        "notes": ""
      },
      {
        "label": "知识掌握",
        "name": "2.课后错题讲解质量+知识点回顾",
        "selectKey": "mistakeKnowledgeReviewQuality",
        "notes": ""
      },
      {
        "label": "精彩时刻",
        "name": "1.听课状态",
        "selectKey": "learningStatus",
        "notes": ""
      },
      {
        "label": "情绪与态度",
        "name": "1.学习积极性",
        "selectKey": "learningEngagement",
        "notes": ""
      },
      {
        "label": "情绪与态度",
        "name": "2.是否有厌学情绪，抵触心理",
        "selectKey": "resistanceSigns",
        "notes": ""
      }
    ]
  }
]
