<!-- 一对多-新班级列表-上课信息对接表弹窗 -->
<template>
  <div v-loading="loading">
    <div style="margin-bottom: 40px; margin-top: -30px; font-size: 15px">
      <span>创建时间：{{ abutmentList.submitTime ? abutmentList.submitTime : abutmentList.updateTime }}</span>
    </div>
    <el-form ref="abutmentList" :model="abutmentList" :label-width="screenWidth > 1300 ? '150px' : '70px'">
      <el-form-item label="学员姓名" prop="studentName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.studentName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="学员编号" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.studentCode }}</span>
        </div>
      </el-form-item>
      <el-form-item label="课程类型" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.curriculumName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="联系方式" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.mobile }}</span>
        </div>
      </el-form-item>
      <div v-if="abutmentList.curriculumCode == 'MATH'">
        <el-form-item label="版本" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.versionName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学科" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.disciplineName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="当前年级" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.gradeName }}</span>
          </div>
        </el-form-item>
      </div>

      <el-form-item label="试课人年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
        </div>
      </el-form-item>
      <el-form-item label="充值课时" prop="rechargeHour" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.rechargeHour }}</span>
        </div>
      </el-form-item>
      <el-form-item label="上课时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-form-item style="margin-bottom: 0 !important" v-if="abutmentList.studyTimeList">
          <el-row style="margin-bottom: 10px; margin-left: 0">
            <el-col :span="24" :xs="24">
              <div class="timeClass" style="margin: 0 0">
                <span style="margin: 0 15px">{{ abutmentList.studyTimeList.map((val) => getWeekName(val.usableWeek)).join('、') }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row style="margin-bottom: 10px; margin-left: 0">
            <el-col :span="24" :xs="24">
              <div class="timeClass" style="margin: 0 0">
                <span style="margin: 0 15px">{{ abutmentList.studyTimeList[0].startTime }}</span>
                <span>至</span>
                <span style="margin: 0 15px">{{ abutmentList.studyTimeList[0].endTime }}</span>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form-item>
      <el-form-item label="是否试课" prop="isExp" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.isExp == 1 ? '是' : '否' }}</span>
        </div>
      </el-form-item>
      <el-form-item
        label="词汇量检测"
        v-if="abutmentList.isExp === 1 && abutmentList.courseType == '鼎英语'"
        prop="wordBase"
        :style="{ width: screenWidth > 1300 ? '50%' : '100%' }"
      >
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.wordBase }}</span>
        </div>
      </el-form-item>
      <el-form-item label="是否新生" prop="isNewStudent" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ abutmentList.isNewStudent == 1 ? '是' : '否' }}</span>
        </div>
      </el-form-item>
      <div v-if="xktDialogVisible === true">
        <el-form-item label="联系人收货地址" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <!-- <el-input v-model="classConnectionData.address" type="textarea" placeholder="请输入" :rows="5" maxlength="200" /> -->
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.address }}</span>
          </div>
        </el-form-item>

        <el-form-item label="学段：" prop="gradeLevel" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <!-- <el-select v-model="classConnectionData.period" clearable>
            <el-option v-for="item in period" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select> -->
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.period }}</span>
          </div>
        </el-form-item>
        <el-form-item label="考试时间：" prop="examTime" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <!-- <el-date-picker v-model="classConnectionData.examTime" type="date" placeholder="选择日期"></el-date-picker> -->
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.examTime }}</span>
          </div>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 23px">{{ abutmentList.remark }}</span>
          <!-- <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" disabled /> -->
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getStudentContactInfoDetail } from '@/api/paikeManage/LearnManager';
  import orderApi from '@/api/oneToManyClass/DispatchInProgress';
  import { GradeType, queryStudentContactInfoDetail } from '@/api/studentClass/changeList';

  export default {
    props: {
      /**
       * 请求类型 1-(默认) 2-（1v多接单的表）3-交付的表
       */
      reqType: {
        type: Number | String,
        required: false,
        default: 1
      },
      id: {
        type: Number | String,
        required: true
      }
    },
    data() {
      return {
        loading: false,
        abutmentList: {},
        screenWidth: window.innerWidth, // 屏幕宽度
        // 年级列表
        options: [
          { value: '1', label: '一年级' },
          { value: '2', label: '二年级' },
          { value: '3', label: '三年级' },
          { value: '4', label: '四年级' },
          { value: '5', label: '五年级' },
          { value: '6', label: '六年级' },
          { value: '7', label: '七年级' },
          { value: '8', label: '八年级' },
          { value: '9', label: '九年级' },
          { value: '10', label: '高中一年级' },
          { value: '11', label: '高中二年级' },
          { value: '12', label: '高中三年级' },
          { value: '13', label: '大一' },
          { value: '14', label: '大二' },
          { value: '15', label: '大三' },
          { value: '16', label: '大四' },
          { value: '17', label: '其他' },
          { value: '18', label: '幼儿园' },
          { value: '19', label: '雅思' },
          { value: '20', label: '托福' }
        ],
        weeklist: [
          { value: 1, label: '周一' },
          { value: 2, label: '周二' },
          { value: 3, label: '周三' },
          { value: 4, label: '周四' },
          { value: 5, label: '周五' },
          { value: 6, label: '周六' },
          { value: 7, label: '周日' }
        ],
        code: [
          'XKT_CZHXN',
          'XKT_CZSXN',
          'XKT_CZWLN',
          'XKT_CZYWN',
          'XKT_CZYYN',
          'XKT_GZDLN',
          'XKT_GZHXN',
          'XKT_GZLSN',
          'XKT_GZSWN',
          'XKT_GZSXN',
          'XKT_GZWLN',
          'XKT_GZYWN',
          'XKT_GZYYN',
          'XKT_GZZZN'
        ],
        xktDialogVisible: false,
        classConnectionData: {},
        period: [
          { value: '2', label: '高中' },
          { value: '1', label: '初中' }
        ]
      };
    },
    watch: {
      id: {
        immediate: true,
        handler(val) {
          this.fillTableNormalData();
        }
      }
    },
    created() {
      this.getGradeList(); // 获取年级列表
    },
    methods: {
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.options = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      // 数字转换星期
      getWeekName(week) {
        let normalWeekData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

        return normalWeekData[Number(week) - 1];
      },
      // 拿到年级
      getGrade(val) {
        let grade = this.options.find((item) => {
          return item.value == val;
        });

        return grade && grade.label;
      },
      //上课信息对接表
      async fillTableNormalData() {
        this.abutmentList = {};
        // console.log(item)

        const that = this;
        this.loading = true;
        if (!this.id) {
          this.$message({
            message: '没有找到该上课信息对接表id',
            type: 'error'
          });
          //   this.$emit('close');
          return;
        }
        const id = this.id;
        let abutment;
        if (this.reqType == 1) {
          console.log('z1111111111');

          abutment = await getStudentContactInfoDetail({ id });
        } else if ([2, 3].includes(this.reqType)) {
          console.log('z22222222222');

          abutment = await orderApi.getClassCourseListFJF(id);
        }

        console.log('🚀 ~ z22222222222- abutment:', abutment.data.curriculumCode);
        this.xktDialogVisible = false;
        if (abutment.data.curriculumCode == 'MATH') {
          let dataDetail = await queryStudentContactInfoDetail(abutment.data.id); // 获取数学-上课信息对接表详情
          console.log('🚀 ~ z22222222222-  dataDetail:', dataDetail);
          that.abutmentList.curriculumCode = 'MATH';
          that.abutmentList.versionName = dataDetail.data.mathExtendedFieldDto.versionName;
          that.abutmentList.disciplineName = dataDetail.data.mathExtendedFieldDto.disciplineName;
          that.abutmentList.gradeName = dataDetail.data.mathExtendedFieldDto.gradeName;
          console.log('🚀 ~ 数学fillTableNormalData ~ that.abutmentList:', that.abutmentList);
        } else if (this.code.includes(abutment.data.curriculumCode)) {
          let tempId = abutment.data.id;
          let tempData = await getStudentContactInfoDetail({ id: tempId });
          console.log('🚀 ~ z22222222222- tempData:', tempData);
          this.xktDialogVisible = true;
          this.classConnectionData.address = tempData.data.extendFieldList[2].fieldValue;
          this.classConnectionData.examTime = tempData.data.extendFieldList[1].fieldValue;
          this.classConnectionData.period = tempData.data.extendFieldList[0].fieldValue == '1' ? '初中' : '高中';
        }

        this.loading = false;

        // if (!this.editForm.experienceObject) {
        //   this.$message({
        //     showClose: true,
        //     message: '请先填写上课信息对接表',
        //     type: 'error'
        //   });
        //   this.$emit('close');
        //   return;
        // }

        // that.abutmentList = abutment.data;
        that.abutmentList = Object.assign(that.abutmentList, abutment.data);
        console.log('🚀 ~ fillTableNormalData ~ that.abutmentList上课信息对接表:', that.abutmentList)`--------------------`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
    ::v-deep.el-date-editor.el-input {
      width: 100%;
    }
    ::v-deep.el-input-number.is-controls-right .el-input__inner {
      text-align: left;
    }
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }
</style>
