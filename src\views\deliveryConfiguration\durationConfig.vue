<template>
  <div class="duration-config">
    <div class="content-wrapper" v-loading="loading">
      <el-form ref="feedbackForm" :model="form" label-width="50px" class="feedback-form">
        <div class="duration-config-dec">填写反馈单后，最小时间间隔依然继续进行补课</div>
        <el-form-item prop="classTimeout" label="n：" class="config-duration">
          <el-input-number v-model="form.classTimeout" :disabled="loading" style="width: 180px" :min="1" controls-position="right" :precision="0" @change="handleCountChange" />
          <span class="duration-config-hour">小时</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer-wrapper">
      <DxFixedFooterButton>
        <el-button type="primary" @click="handleSave" :disabled="saveLoading" :loading="saveLoading">确定</el-button>
      </DxFixedFooterButton>
    </div>
  </div>
</template>
<script>
  import DxFixedFooterButton from '@/components/DxFixedFooterButton/index.vue';
  import { getClassTimeoutConfig, saveClassTimeoutConfig } from '@/api/systemConfig/durationConfig';
  export default {
    name: 'durationConfig',
    components: { DxFixedFooterButton },
    data() {
      return {
        form: {
          id: null,
          classTimeout: null //超时时间
        },
        saveLoading: false,
        loading: false
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.getDurationList();
    },
    watch: {},
    methods: {
      async getDurationList() {
        this.loading = true;
        try {
          const res = await getClassTimeoutConfig();
          const { id, classTimeOut } = res.data;
          this.form.id = id;
          this.form.classTimeout = classTimeOut;
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
      },
      handleCountChange(val) {
        this.form.classTimeout = val;
      },
      async handleSave() {
        try {
          this.saveLoading = true;
          const res = await saveClassTimeoutConfig({
            ...this.form
          });
          this.$message.success('保存成功');
        } catch (e) {
          console.error('保存失败:', e);
        } finally {
          this.saveLoading = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .duration-config {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 84px);
  }

  .content-wrapper {
    flex: 1;
    padding: 20px 20px 20px 20px;
    overflow-y: auto;
  }

  .footer-wrapper {
    flex-shrink: 0;
  }

  .duration-config-dec {
    font-size: 14px;
    color: #000;
    margin-bottom: 20px;
  }
  .duration-config-hour {
    margin-left: 10px;
  }
</style>
