// 新班级列表/等待成班中 接口
import request from '@/utils/request';

/**
 * -----------正式课
 */

// 获取学员列表
export function getStudentList(params) {
  return request({
    url: '/deliver/web/oneMore/getOneMoreWaitDeliverStudentList',
    method: 'get',
    params: {
      ...params
    }
  });
}

/**
 * -----------试课
 */

// 获取学员列表
export function getStudentList2(params) {
  return request({
    url: '/deliver/web/oneMore/getOneMoreWaitExperienceStudentList',
    method: 'get',
    params: {
      ...params
    }
  });
}
// zx查询试课单
export function getTrialInfo(orderId) {
  return request({
    url: '/zx/exp/getInfo',
    method: 'get',
    params: {
      orderId
    }
  });
}

//待成班的列表查询
export const getWaitingClassPageList = (params) =>
  request({
    url: '/deliver/class/studentLeave/getWaitingClassPageList',
    method: 'get',
    params
  });
//补课 派单中列表
export const getTemporaryClassList = (params) =>
  request({
    url: '/deliver/temporary/class/getTemporaryClassList',
    method: 'get',
    params
  });
//临时班级表新增数据
export const addTemporaryClass = (data) =>
  request({
    url: '/deliver//temporary/class/addTemporaryClass',
    method: 'post',
    data
  });
