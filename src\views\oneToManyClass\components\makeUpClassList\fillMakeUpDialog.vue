<template>
  <div>
    <el-dialog title="填写补课单" width="600px" :visible.sync="dialogVisible" destroy-on-close append-to-body>
      <el-form ref="fillMakeUpForm" :model="fillMakeUpForm" label-position="right" :rules="rules" label-width="120px">
        <el-form-item label="课程类型：" prop="curriculumName">
          <el-input v-model="fillMakeUpForm.curriculumName" placeholder="请输入" disabled />
        </el-form-item>
        <el-form-item label="补课人姓名：" prop="studentName">
          <el-input v-model="fillMakeUpForm.studentName" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="补课人手机号：" prop="phone">
          <el-input v-model="fillMakeUpForm.phone" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="补课人年级：" prop="grade">
          <el-select v-model="fillMakeUpForm.grade" disabled clearable filterable class="full-width" placeholder="请选择补课人年级">
            <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学员性别：" prop="gender" :inline="true">
          <el-select v-model="fillMakeUpForm.gender" disabled clearable filterable class="full-width" placeholder="请选择学员性别">
            <el-option label="男" value="1"></el-option>
            <el-option label="女" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请假课程时间：" prop="leaveTime">
          <el-input v-model="fillMakeUpForm.leaveTime" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="补课时间：" prop="makeUpClassTime">
          <!--isExperience false 为2 为正课-->
          <BaseClassStudyTimeSelect
            v-model="fillMakeUpForm.makeUpClassTime"
            :curriculumId="fillMakeUpForm.curriculumId"
            :grade="fillMakeUpForm.grade"
            placeholder="请选择补课时间"
            :isExperience="false"
            @change="handleClassTimeConfigIdChange"
          ></BaseClassStudyTimeSelect>
        </el-form-item>
        <el-form-item label="学员所在区域" prop="address">
          <el-input v-model="fillMakeUpForm.address" disabled placeholder="请输入" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import BaseClassStudyTimeSelect from '@/views/oneToManyClass/components/pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';
  import { GradeType } from '@/api/studentClass/changeList';
  import { getLessonsList, updateLeaveDate } from '@/api/oneToManyClass/pendingCompletionClassInfo';
  export default {
    name: 'fillMakeUpDialog',
    components: { BaseClassStudyTimeSelect },
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      clickRowData: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        fillMakeUpForm: {
          curriculumName: '', // 课程类型
          studentName: '', // 补课人姓名
          phone: '', // 补课手机号
          grade: '', // 补课年级
          gender: '', // 学员性别
          leaveTime: '', // 请假课程时间
          makeUpClassTime: '', // 补课时间
          address: '', // 学员所在区域
          curriculumCode: '',
          studentCodes: [], //学生code
          classStudentLeaveIds: [] // 请假id
        },
        gradeList: [], // 补课人年级
        rules: {
          makeUpClassTime: [{ required: true, message: '请选择', trigger: 'change' }]
        },
        _isMounted: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      dialogVisible(val) {
        if (val && this._isMounted) {
          const { curriculumName, curriculumId, studentName, phone, curriculumCode, leaveTime, studentCode, id } = this.clickRowData;
          this.fillMakeUpForm = {
            ...this.fillMakeUpForm,
            curriculumName,
            studentName,
            curriculumId,
            phone,
            leaveTime,
            curriculumCode,
            studentCode,
            id
          };
          this.fillMakeUpForm.studentCodes = studentCode && [studentCode];
          this.fillMakeUpForm.classStudentLeaveIds = id && [id];
          this.getGradeList();
          this.getInfo();
        }
      }
    },
    created() {},
    mounted() {
      this._isMounted = true;
    },
    methods: {
      async getInfo() {
        try {
          const params = {
            studentCodes: this.fillMakeUpForm.studentCodes,
            classStudentLeaveIds: this.fillMakeUpForm.classStudentLeaveIds
          };
          const res = await getLessonsList({ ...params });
          const { address = '', gender = '', grade = '', id = '' } = res.data[0] || {};
          console.log(res.data, 'res.data');
          this.fillMakeUpForm.address = address;
          this.fillMakeUpForm.gender = gender === '0' ? '男' : gender === '1' ? '女' : '';
          this.fillMakeUpForm.grade = String(grade);
          this.id = id;
        } catch (e) {
          console.log(e);
        }
      },
      // 重置表单
      resetForm() {
        this.$refs.fillMakeUpForm.resetFields();
        this.fillMakeUpForm.address = [];
      },
      // 获取年级下拉列表
      async getGradeList() {
        try {
          const res = await GradeType();
          this.gradeList = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        } catch (e) {
          console.log(e);
        }
      },
      // 处理上课时间配置id改变事件
      handleClassTimeConfigIdChange(obj) {
        console.log(obj, 'handleClassTimeConfigIdChange');
        const weekMap = { 0: '日', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '日' };
        if (Array.isArray(obj) && obj.length > 0) {
          this.fillMakeUpForm.makeUpClassTime =
            obj
              .sort((a, b) => a.usableWeek - b.usableWeek)
              .map((item) => `周${weekMap[item.usableWeek]}`)
              .join('、') + `${obj[0].startTime.trim()}-${obj[0].endTime.trim()}`;
        }
        console.log(this.fillMakeUpForm, '入参');
      },
      // 确定按钮校验
      onSubmit() {
        this.$refs.fillMakeUpForm.validate((valid) => {
          if (valid) {
            this.submitForm();
          } else {
            this.$message.warning('请完善表单信息');
            return false;
          }
        });
      },
      // 提交
      async submitForm() {
        try {
          const res = await updateLeaveDate({
            id: this.fillMakeUpForm.id,
            makeUpClassTime: this.fillMakeUpForm.makeUpClassTime
          });
          this.$message.success('提交成功');
          this.$emit('submit');
          this.dialogVisible = false;
          this.resetForm();
        } catch (e) {
          console.log(e);
        }
      }
    }
  };
</script>
