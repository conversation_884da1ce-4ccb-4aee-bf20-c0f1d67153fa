<!-- 交付中心-一对多学员管理-学习课程表 -->
<template>
  <div>
    <!-- 查询栏 -->
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" ref="searchNum" :model="searchNum" :inline="true" style="display: flex; flex-wrap: wrap">
        <el-form-item label="班级名称:" prop="className">
          <el-input v-model.trim="searchNum.className" clearable placeholder="请输入班级名称"></el-input>
        </el-form-item>
        <el-form-item label="班级编号:" prop="classCode">
          <el-input v-model.trim="searchNum.classCode" clearable placeholder="请输入班级编号"></el-input>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId" style="display: flex">
          <el-select v-model="searchNum.curriculumId" filterable clearable placeholder="请选择">
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类:" prop="type">
          <el-select v-model="displayType" clearable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in courseTypeListSearch" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="isAdmin">
          <el-form-item label="交付中心编号:" prop="deliverMerchant">
            <el-input v-model.trim="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号"></el-input>
          </el-form-item>
          <el-form-item label="交付中心名称:" prop="deliverMerchant1">
            <BaseElSelectLoadmore v-model="searchNum.deliverMerchant1" valueProp="merchantCode" labelProp="merchantName" :searchFunc="getDeliverList"></BaseElSelectLoadmore>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      ref="configurationTable"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 550 : 200"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }" v-if="needTableSlotProp.includes(item.value)">
          <div v-if="item.value == 'deliverClassStudentListText'">
            <span>{{ row.deliverClassStudentListText }}</span>
          </div>
          <div v-if="item.value == 'courseType'">
            <span v-if="row.courseType == 1">试课</span>
            <span v-if="row.courseType == 2 && !row.lessonsFlag">正式课</span>
            <span v-if="row.courseType == 2 && row.lessonsFlag == 1">补课</span>
          </div>
          <div v-else-if="item.value == 'teacherName'">
            <el-tag v-if="row.teacherTypeDev == 1" size="mini" effect="dark" type="success">上</el-tag>
            <el-tag v-if="row.teacherTypeDev == 0" size="mini" effect="dark" type="warning">试</el-tag>
            <el-tag v-if="row.teacherTypeDev == 3" size="mini" effect="dark" type="">代</el-tag>
            {{ row.teacherName }}
          </div>
          <div v-else-if="item.value == 'studyStatus'">
            {{ commonFormat(studyStatusList, row.studyStatus, '') }}
          </div>
          <div v-else-if="item.value == 'operate'">
            <el-button v-if="row.studyStatus === 2" type="success" size="mini" @click="handleLookDataClick(row)">数据查看</el-button>
            <el-button v-if="row.studyStatus === 0" type="danger" size="mini" @click="handleDeleteClick(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 数据查看弹窗 -->
    <StudyScheduleDataLook :visible.sync="dataLookVisible" :loading="dataLookLoading" :data-list="dataLookData" ref="StudyScheduleDataLook" />
    <StudyScheduleMathDataLook :visible.sync="dataLookMathVisible" :loading="dataLookMathLoading" :data-list="dataLookMathData" ref="StudyScheduleMathDataLook" />
    <StudyScheduleXktDataLook ref="StudyScheduleXktDataLook" />

    <!-- 表头设置弹窗 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import BaseElSelectLoadmore from './components/studentList/BaseElSelectLoadmore.vue';
  import StudyScheduleDataLook from './components/studySchedule/StudyScheduleDataLook.vue';
  import StudyScheduleMathDataLook from './components/studySchedule/StudyScheduleMathDataLook.vue';
  import StudyScheduleXktDataLook from './components/studySchedule/StudScheduleXktDataLook.vue';
  import { getStudyScheduleData, getFeedbackInfo, getMathFeedbackInfo, setStudyScheduleDelete, getXktFeedbackInfo } from '@/api/oneToManyClass/studySchedule';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import { GradeType } from '@/api/studentClass/changeList';
  import { getAllDeliver } from '@/api/oneToManyClass/classList';
  import ls from '@/api/sessionStorage';
  export default {
    name: 'StudySchedule',
    components: { HeaderSettingsDialog, BaseElSelectLoadmore, StudyScheduleDataLook, StudyScheduleMathDataLook, StudyScheduleXktDataLook },
    data() {
      return {
        isAdmin: false,
        isDeliveryCenter: false,

        // 搜索栏
        searchNum: {
          gradeIdDev: [],
          type: ''
        },
        displayType: '', // 分类 单独处理补课
        curriculumList: [],
        courseTypeListSearch: [
          { value: '1', label: '试课' },
          { value: 'makeUp', label: '补课' }, // 自定义 如果是补课传  lessonsFlag: 1 否者 0
          { value: '0', label: '正式课' }
        ],
        gradeList: [],
        studyStatusList: [
          { value: '0', label: '未上课' },
          { value: '1', label: '已上课未反馈' },
          { value: '2', label: '已完成' }
        ],

        // 列表属性弹框
        HeaderSettingsStyle: false,
        tableHeaderList: [],
        headerSettings: [
          { name: '班级名称', value: 'className' },
          { name: '班级编号', value: 'classCode' },
          { name: '上课时间', value: 'studyDate' },
          { name: '课程类型', value: 'curriculumName' },
          { name: '分类', value: 'courseType' },
          { name: '交付小组', value: 'deliverGroup' },
          { name: '教练老师', value: 'teacherName' },
          { name: '年级', value: 'gradeName' },
          { name: '上课学员', value: 'deliverClassStudentListText' },
          { name: '上课人数', value: 'studyStudentCount' },
          { name: '状态', value: 'studyStatus' },
          { name: '操作', value: 'operate' }
        ],

        // 表格数据
        tableLoading: false,
        needTableSlotProp: ['courseType', 'teacherName', 'studyStatus', 'operate'],
        tableList: [],
        courseTypeList: [
          { value: '1', label: '试课' },
          { value: '2', label: '正式课' }
        ],

        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },

        //数据查看弹窗数据
        dataLookVisible: false,
        dataLookMathVisible: false,
        dataLookLoading: false,
        dataLookMathLoading: false,
        dataLookXktVisible: false,
        dataLookXktLoading: false,
        dataLookData: {},
        dataLookMathData: {},
        dataLookXktData: {},

        code: [
          'XKT_CZHXN',
          'XKT_CZSXN',
          'XKT_CZWLN',
          'XKT_CZYWN',
          'XKT_CZYYN',
          'XKT_GZDLN',
          'XKT_GZHXN',
          'XKT_GZLSN',
          'XKT_GZSWN',
          'XKT_GZSXN',
          'XKT_GZWLN',
          'XKT_GZYWN',
          'XKT_GZYYN',
          'XKT_GZZZN'
        ]
      };
    },
    watch: {
      displayType(val) {
        // 如果是补课 type0 lessonsFlag 1
        if (val == 'makeUp') {
          this.searchNum.type = 0;
          this.searchNum.lessonsFlag = 1;
        } else if (val == '0') {
          // 如果是正课 type0 lessonsFlag 0
          this.searchNum.type = val;
          this.searchNum.lessonsFlag = 0;
        } else {
          // 如果是试课 type1 lessonsFlag undefined
          this.searchNum.type = val || '';
          this.searchNum.lessonsFlag = undefined;
        }
      }
    },
    created() {
      // 是否是管理员(dxAdmin+187)
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';

      // 是否是交付中心
      this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';

      if (this.isAdmin) {
        this.headerSettings.splice(4, 0, { name: '交付中心名称', value: 'deliverName' }, { name: '交付中心编号', value: 'deliverMerchant' });
      }

      // 获取表头设置
      this.getHeaderlist();
    },
    mounted() {
      this.getCurriculumList();
      this.getGradeList();
      let classCode = this.$route.query.classCode;
      if (classCode) {
        this.$set(this.searchNum, 'classCode', classCode);
      }
      this.getStudyScheduleList();
    },
    methods: {
      // 获取课程类型下拉列表
      getCurriculumList() {
        getAllOneToManyCurriculumType().then((res) => {
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      // 获取年级下拉列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      // 新增/编辑 弹窗 获取交付中心分页下拉
      getDeliverList(selectQuery) {
        return new Promise((resolve, reject) => {
          getAllDeliver(selectQuery)
            .then((res) => {
              resolve(
                res.data.data.map((item) => {
                  return { value: item.merchantCode, label: item.merchantName };
                })
              );
            })
            .catch((err) => reject(err));
        });
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      // 同步列表属性弹窗开启状态
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'StudySchedule',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'StudySchedule'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = [];
            JSON.parse(res.data.value).forEach((item) => {
              if (item) {
                this.tableHeaderList.push(item);
              }
            });
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 表格动态class
      getRowClass({ rowIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getStudyScheduleList();
      },
      // 重置
      reset() {
        this.searchNum = {};
        this.displayType = '';
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getStudyScheduleList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getStudyScheduleList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getStudyScheduleList();
      },
      // 分页获取学习课程表数据
      getStudyScheduleList() {
        this.tableLoading = true;
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getStudyScheduleData(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data || [];
            this.tableList.forEach((each) => {
              if (Array.isArray(each.deliverClassStudentList)) {
                each.deliverClassStudentListText = each.deliverClassStudentList
                  .map((item) => {
                    return `${item.studentName}（${item.studentCode}）${item.leaveFlag ? '-已请假' : ''}`;
                  })
                  .join('、');
              } else {
                each.deliverClassStudentListText = '-';
              }
            });
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableList = [];
            this.tableLoading = false;
          });
      },

      //打开学考通弹窗
      openXktDrawer(data, loading) {
        this.$refs.StudyScheduleXktDataLook?.show(data, loading);
      },
      loadData(data, loading) {
        this.$refs.StudyScheduleXktDataLook?.loadData(data, loading);
      },
      closeXktDrawer() {
        this.$refs.StudyScheduleXktDataLook?.closeDrawer();
      },

      // 处理查看数据点击事件
      handleLookDataClick(row) {
        console.log('查看数据', row);
        if (row.curriculumCode == 'MATH') {
          this.dataLookMathData = {};
          this.dataLookMathLoading = true;
          this.dataLookMathVisible = true;
          getMathFeedbackInfo({ id: row.id, isShow: 1, courseId: '', courseType: '' })
            .then((res) => {
              console.log('数学反馈', res.data);
              if (!res.data) {
                this.dataLookMathVisible = false;
                this.$message.error('没有获取到排课学习时间信息');
                return;
              }
              this.dataLookMathData = res.data;
              this.dataLookMathLoading = false;
            })
            .catch((err) => {
              console.error('获取数学反馈信息失败', err);
              this.dataLookMathVisible = false;
            });
        } else if (this.code.includes(row.curriculumCode)) {
          this.dataLookXktData = {};
          this.dataLookXktLoading = true;
          this.openXktDrawer(this.dataLookXktLoading);
          getXktFeedbackInfo({ id: row.id, isShow: 1 })
            .then((res) => {
              if (!res.data) {
                this.dataLookXktVisible = false;
                this.$message.error('没有获取到排课学习时间信息');
                return;
              }
              this.dataLookXktLoading = false;
              this.dataLookXktData = res.data;
              this.loadData(res.data, this.dataLookXktLoading);
            })
            .catch(() => {
              this.closeXktDrawer();
            });
        } else {
          this.dataLookData = {};
          this.dataLookLoading = true;
          this.dataLookVisible = true;
          getFeedbackInfo({ classPlanStudyId: row.id })
            .then((res) => {
              if (!res.data) {
                this.dataLookVisible = false;
                this.$message.error('没有获取到排课学习时间信息');
                return;
              }
              this.dataLookData = res.data;
              let studentNames = '';
              this.dataLookData.deliverClassStudentList.forEach((each) => {
                studentNames += each.studentName + '（' + each.studentCode + '）、';
              });
              this.dataLookData.studentNames = studentNames.substring(0, studentNames.length - 1);
              this.dataLookLoading = false;
            })
            .catch(() => {
              this.dataLookVisible = false;
            });
        }
      },
      // 处理删除点击事件
      handleDeleteClick(row) {
        let { id, courseType } = row;
        this.$confirm('您确定要删除学习课程吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            setStudyScheduleDelete(id, courseType).then(() => {
              this.$message.success('删除成功');
              this.getStudyScheduleList();
            });
          })
          .catch(() => {
            this.$message.info('已取消删除');
          });
      },
      commonFormat(array, value, emptyText = '无') {
        let item = array.find((item) => item.value == value);
        if (item) {
          return item.label;
        }
        return emptyText;
      }
    }
  };
</script>

<style></style>
