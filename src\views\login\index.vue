<template>
  <div class="login-container">
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="login" />
    <div class="bg" v-if="isdz == 1">
      <img src="../../assets/<EMAIL>" class="bg-img" />
      <img src="../../assets/logo2.png" class="logo1" />
      <div class="login_form">
        <el-form ref="loginForm" class="login-form1" auto-complete="on" :model="loginForm" :rules="loginRules" label-position="left">
          <div class="title-container">
            <h3 class="title1">鼎校交付系统</h3>
          </div>
          <el-form-item prop="username">
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="user" />
            </span>
            <el-input v-model="loginForm.username" name="username" type="text" autocomplete="on" placeholder="帐号" @blur="blurChange(loginForm.username)" />
          </el-form-item>
          <el-form-item>
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="userCode" />
            </span>
            <el-select v-model="loginForm.role" placeholder="请选择角色" style="width: 90%" :disabled="roleDisable">
              <el-option v-for="item in roleTagList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="password">
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="password" />
            </span>
            <el-input v-model="loginForm.password" name="password" :type="passwordType" auto-complete="on" placeholder="密码" />
            <span class="show-pwd" @click="showPwd">
              <svg-icon icon-class="eye" />
            </span>
          </el-form-item>

          <el-button class="login-btn" @click="handleLogin" type="primary" style="" :loading="loading">登录</el-button>
          <div class="forget" @click="forgot()">忘记账号密码？</div>
        </el-form>
      </div>
    </div>
    <div class="bg1" v-if="isdz == 2">
      <img src="../../assets/<EMAIL>" class="bg-img" />
      <div class="logo2">
        <img :src="logoData.avatar" style="height: 100%" />
        <img src="../../assets/logo2.png" style="height: 100%" class="logo3" />
      </div>
      <div class="login_form2">
        <el-form ref="loginForm" class="login-form2" auto-complete="on" :model="loginForm" :rules="loginRules" label-position="left">
          <div class="title-container">
            <h3 class="title1">{{ logoData.customName }}鼎校甄选交付系统</h3>
          </div>
          <el-form-item prop="username">
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="user" />
            </span>
            <el-input v-model="loginForm.username" name="username" style="color: #257acb" type="text" autocomplete="on" placeholder="帐号" @blur="blurChange(loginForm.username)" />
          </el-form-item>
          <el-form-item>
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="userCode" />
            </span>
            <el-select v-model="loginForm.role" placeholder="请选择角色" style="width: 90%; font-size: 0.729vw" :disabled="roleDisable">
              <el-option v-for="item in roleTagList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="password">
            <span class="svg-container svg-container_login">
              <svg-icon icon-class="password" />
            </span>
            <el-input v-model="loginForm.password" name="password" :type="passwordType" auto-complete="on" placeholder="密码" />
            <span class="show-pwd" @click="showPwd">
              <svg-icon icon-class="eye" />
            </span>
          </el-form-item>

          <el-button class="login-btn1" @click="handleLogin" type="primary" style="" :loading="loading">登录</el-button>
          <div class="forget" @click="forgot()">忘记账号密码？</div>
        </el-form>
      </div>
    </div>
    <!--弹出窗口：修改密码-->
    <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" :visible.sync="dialogPasswordVisible" width="40%" :modal-append-to-body="false" :show-close="false">
      <div class="dialog-title">为了您的账户安全,请修改登录密码</div>
      <el-form :rules="rules" ref="dataForm" :model="temp" class="updatePassword">
        <el-form-item prop="newPwd" style="width: 100%; text-align: center">
          <el-input :type="passwordNewType" v-model="temp.newPwd" placeholder="输入新密码" style="width: 100%">
            <template #suffix>
              <span class="svg-container-end" @click="showNewPwd">
                <svg-icon :icon-class="passwordNewType == 'password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmPwd" style="width: 100%; text-align: center">
          <el-input :type="passwordComType" v-model="temp.confirmPwd" placeholder="确认新密码" style="width: 100%">
            <template #suffix>
              <span class="svg-container-end" @click="showComPwd">
                <svg-icon :icon-class="passwordComType == 'password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="smsCode" style="width: 100%; text-align: center">
          <div style="display: flex; justify-content: center">
            <el-input placeholder="请输入短信验证码" v-model="temp.smsCode" style="flex: 1"></el-input>
            <el-button @click.stop="getSmsClick()" :disabled="disabledSmsClick" type="primary">{{ count }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-tip">温馨提示:</div>
      <div class="dialog-tip">1:新密码长度不少于8位,需包含字母、数字;</div>
      <div class="dialog-tip">2:如果您的登录号码收不到验证码，请联系渠道经理修改登录手机号;</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updatePwd" size="medium">确定</el-button>
        <el-button @click="closeDialog" style="margin-left: 40px" size="medium">取消</el-button>
      </div>
    </el-dialog>
    <Verify ref="changeVerify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" style="z-index: 10000 !important" />
  </div>
</template>

<script>
  import { removeToken } from '@/utils/auth';
  import { isvalidUsername } from '@/utils/validate';
  import auth from '@/api/auth';
  import LangSelect from '@/components/LangSelect';
  //import captcha from 'vue-social-captcha'
  import Verify from '@/components/verifition/Verify';
  import forgotApi from '@/api/forgot';
  import userApi from '@/api/user';

  export default {
    name: 'Login',
    components: {
      LangSelect,
      Verify
    },
    async created() {
      this.refreshVerificationCode();

      const Loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 1)'
      });

      const host = window.location.host;
      const isLocalhost = /^[0-9.]+:[0-9]+$/.test(host);
      const isNgrokDomain = host.includes('ngrok');

      // 本地环境或 ngrok 环境，使用默认配置
      if (isLocalhost || isNgrokDomain) {
        this.isdz = 1;
        Loading.close();
        return;
      }

      const subdomain = host.split('.')[0];
      const isDeliverDomain = subdomain.includes('deliver');

      if (isDeliverDomain) {
        // deliver 相关域名，使用默认配置
        this.isdz = 1;
        Loading.close();
      } else {
        // 其他域名，获取自定义 logo 和标题
        this.url = subdomain.slice(0, -1);
        try {
          const response = await auth.getLogo(this.url);
          this.logoData = response.data;
          if (this.logoData) {
            document.title = this.logoData.customName + '鼎校甄选';
          }
          this.isdz = 2;
        } catch (error) {
          console.error('获取 logo 失败:', error);
          this.isdz = 1; // 失败时使用默认配置
        } finally {
          Loading.close();
        }
      }
    },
    async beforeRouteLeave(to, from, next) {
      if (this.dialogPasswordVisible) {
        // 阻止路由跳转
        await this.logout(true);
        next(false);
      } else {
        next();
      }
    },
    beforeDestroy() {
      // 确保页面销毁时移除监听
      window.removeEventListener('beforeunload', this.handleBeforeUnload);
    },
    watch: {
      passwordc(val) {
        if (val && this.usernamec) {
          this.loginDisabled = false;
        }
        console.log(this.loginDisabled);
      },
      usernamec(val) {
        if (val && this.passwordc) {
          this.loginDisabled = false;
        }
        console.log(this.loginDisabled);
      },
      dialogPasswordVisible(val) {
        if (val) {
          // 弹窗打开时监听刷新
          window.addEventListener('beforeunload', this.handleBeforeUnload);
        } else {
          // 弹窗关闭时移除监听
          window.removeEventListener('beforeunload', this.handleBeforeUnload);
        }
      }
    },

    data() {
      const validateUsername = (rule, value, callback) => {
        if (!isvalidUsername(value)) {
          callback(new Error('请输入正确的用户帐号'));
        } else {
          callback((this.usernamec = true));
          this.$refs['loginForm'].clearValidate();
        }
      };

      const validatePassword = (rule, value, callback) => {
        if (value.length < 6) {
          callback(new Error('密码不能少于6个字符'));
        } else {
          callback((this.passwordc = true));
          this.$refs['loginForm'].clearValidate();
        }
      };
      let validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else if (value.length < 8) {
          callback(new Error('密码不能少于8个字符'));
        } else if (value.length > 50) {
          callback(new Error('密码不能多于50个字符'));
        } else {
          const regexForbidden = /[\u4e00-\u9fa5\s\W_]/; // 匹配汉字和空格
          if (regexForbidden.test(value)) {
            callback(new Error('密码不能包含汉字和空格和符号'));
            return;
          }
          const regex = /^(?=.*[A-Za-z])(?=.*\d).+$/;
          if (!regex.test(value)) {
            callback(new Error('密码需同时包含字母和数字'));
          }
          if (this.temp.confirmPwd !== '') {
            this.$refs.dataForm.validateField('confirmPwd');
          }
          callback();
        }
      };

      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value != this.temp.newPwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        // captchaOption: {
        //   // 各平台的参数，具体请参阅个平台文档
        //   // 以下为腾讯验证码的参数
        //   appid: '2027404493'
        //   // 以下为极验验证码的参数
        //   //product: 'bind',
        // },
        isdz: 0,
        url: '',
        logoData: {},
        verificationCodeUrl: '',
        loginDisabled: false,
        usernamec: false,
        passwordc: false,
        roleDisable: false,
        loginForm: {
          username: '',
          password: '',
          verificationCode: '',
          userType: 8,
          role: ''
        },
        loginRules: {
          username: [
            {
              required: true,
              trigger: 'blur',
              validator: validateUsername
            }
          ],
          password: [
            {
              required: true,
              trigger: 'blur',
              validator: validatePassword
            }
          ],
          role: [
            {
              required: true,
              trigger: 'blur',
              validator: '必填'
            }
          ]
        },
        isRouterAlive: true,
        passwordType: 'password',
        loading: false,
        showDialog: false,
        roleTagList: [],
        dialogPasswordVisible: false,
        temp: {
          mobile: '',
          newPwd: '',
          confirmPwd: '',
          smsCode: '',
          source: 'admin',
          sysType: 0
        },
        rules: {
          newPwd: [{ validator: validatePass, trigger: 'blur' }],
          confirmPwd: [{ validator: validatePass2, trigger: 'change' }],
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { message: '验证码不得小于6位', trigger: 'blur' }
          ]
        },
        count: '获取验证码',
        disabledSmsClick: false,
        passwordNewType: 'password',
        passwordComType: 'password'
      };
    },
    methods: {
      refreshVerificationCode() {
        let globalId = this.$store.getters.globalId;
        if (globalId === undefined || globalId == null || globalId === '') {
          globalId = this.uuid().toString();
        }

        this.$store.commit('SET_globalId', globalId);
        //请求验证码
        auth.verificationCode(globalId).then((res) => {
          this.verificationCodeUrl = res.data;
        });
      },
      // 获取当前登录用户手机号
      getPhoneNum() {
        userApi.getPhone().then((res) => {
          this.temp.mobile = res.data.phone;
          // this.temp.mobile = '18856908221';
        });
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      //根据登录账号获取角色
      blurChange(ele) {
        if (ele != null && ele != undefined && ele != '' && ele != '' && ele.length > 0) {
          auth.byLoginNameQueryRoleTag(ele).then((res) => {
            //局部刷新一下
            this.roleTagList = res.data;
            if (this.roleTagList.length > 0) {
              if (this.roleTagList.length == 1) {
                this.roleDisable = true;
                this.loginForm.role = this.roleTagList[0].value;
              } else {
                this.roleDisable = false;
              }
            } else {
              this.loginForm.role = '';
            }
          });
        }
      },
      uuid() {
        var s = [];
        var hexDigits = '0123456789abcdef';
        for (var i = 0; i < 36; i++) {
          s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
        }
        s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
        s[8] = s[13] = s[18] = s[23] = '-';
        var uuid = s.join('');
        return uuid;
      },
      captchaNotice(status, res) {
        if (res.success) {
          this.handleLogin();
        } else {
          this.$message.error(res.message);
        }
      },
      changeRoleTag() {
        auth.byLoginNameQueryRoleTag(this.loginForm.username).then((res) => {
          //局部刷新一下
          this.roleTagList = res.data;
          console.log(this.roleTagList.length + 'wyy');
          if (this.roleTagList.length > 0) {
            this.loginForm.role = this.roleTagList[0].value;
          } else {
            this.loginForm.role = '';
          }
        });
      },
      showPwd() {
        if (this.passwordType === 'password') {
          this.passwordType = '';
        } else {
          this.passwordType = 'password';
        }
      },

      //发送验证码
      getSmsClick() {
        if (this.temp.newPwd == '' || this.temp.confirmPwd == '') {
          this.$message.error('请输入密码');
          return;
        }
        this.sendSmg();
      },
      sendSmg() {
        forgotApi.sendSmg(this.temp.mobile).then((res) => {
          console.log(res);
          this.$message.success('短信验证码发送成功，请注意查收');
          var num = 60;
          this.disabledSmsClick = true;
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (num > 0) {
                num--;
                this.count = num + 's';
              } else {
                clearInterval(this.timer);
                this.timer = null;
                this.count = '重新获取验证码';
                this.disabledSmsClick = false;
              }
            }, 1000);
          }
        });
      },
      updatePwd() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          const tempData = Object.assign({}, this.temp); //copy obj
          forgotApi.pswReset(tempData).then((res) => {
            this.dialogPasswordVisible = false;
            this.$message.success('修改密码成功，请重新登录');
            this.logout();
          });
        });
      },
      closeDialog() {
        this.dialogPasswordVisible = false;
        this.temp = {
          mobile: '',
          smsCode: '',
          newPwd: '',
          confirmPwd: ''
        };
        this.$refs.dataForm.resetFields();
        this.logout();
      },

      login(params) {
        this.loginForm.verificationCode = params.captchaVerification;
        this.loading = true;
        this.$store
          .dispatch('LoginByUsername', this.loginForm)
          .then(async (res) => {
            this.loading = false;
            if (window.localStorage.getItem('role') == 'JiaofuManager' && localStorage.getItem('firstLoginTag') == 'true') {
              await this.$refs.verify.closeBox();
              await this.getPhoneNum();
              this.dialogPasswordVisible = true;
            } else {
              window.localStorage.setItem('Name', this.loginForm.username);
              let { data } = await auth.getUserInfo();
              //如果是资转师，去资转订单页面(资转师没有学员列表页面权限)
              const isassetTransfer = data.roles.some((item) => item.val == 'assetTransfer'); //是否资转师
              if (isassetTransfer) {
                return this.$router.push({
                  path: '/transferOrder/index/transferOrder_index'
                });
              }
              if (/^[0-9.]+:[0-9]+$/.test(window.location.host) || window.location.host.concat('ngrok')) {
                return this.$router.push({
                  path: '/'
                });
              }
              if (this.logoData && this.logoData.logoEnable) {
                this.$store.commit('setJlbInfo', this.logoData);
                return this.$router.push({
                  path: '/'
                });
              }
              let currentAdminVo = data.currentAdminVo;
              if (!currentAdminVo && this.url == 'deliver')
                return this.$router.push({
                  path: '/'
                });
              if (!currentAdminVo && this.url != 'deliver') {
                // debugger;
                this.$store.commit('SET_TOKEN', '');
                removeToken();
                window.location.href = 'https://deliver.dxznjy.com/#/pclass/index?token=' + res;
              }
              if (!currentAdminVo.secondaryDomainName && this.url == 'deliver')
                return this.$router.push({
                  path: '/'
                });
              if (this.url == currentAdminVo.secondaryDomainName) {
                return this.$router.push({
                  path: '/'
                });
              } else {
                this.$store.commit('SET_TOKEN', '');
                removeToken();
                if (currentAdminVo.secondaryDomainName && currentAdminVo.logoEnable == 1) {
                  window.location.href = `https://${currentAdminVo.secondaryDomainName}d.dxznjy.com/#/pclass/index?token=` + res;
                } else {
                  window.location.href = 'https://deliver.dxznjy.com/#/pclass/index?token=' + res;
                }
              }
            }
          })
          .catch((e) => {
            console.log(e);
            // this.refreshVerificationCode();
            this.loading = false;
          });
      },
      handleBeforeUnload(event) {
        this.logout(true); // 传 true，强制清除
        event.preventDefault();
        event.returnValue = '';
      },
      logout(isForce = false) {
        if (isForce) {
          this.$store.commit('SET_TOKEN', '');
          this.$store.commit('SET_ROLES', []);
          removeToken();
          return;
        }
        this.$store.dispatch('LogOut').then(() => {
          location.reload();
        });
      },
      showNewPwd() {
        if (this.temp.newPwd == '') return;
        if (this.passwordNewType === 'password') {
          this.passwordNewType = '';
        } else {
          this.passwordNewType = 'password';
        }
      },
      showComPwd() {
        if (this.temp.confirmPwd == '') return;
        if (this.passwordComType === 'password') {
          this.passwordComType = '';
        } else {
          this.passwordComType = 'password';
        }
      },
      handleLogin() {
        if (this.loginForm.username === '' || this.loginForm.password === '') {
          this.$message.error('请输入用户名或密码！');
          return false;
        }
        this.$refs.verify.show();
      },
      // 跳转到忘记密码
      forgot() {
        this.$router.push({
          path: '/forgot'
        });
      }
    }
  };
</script>
<style rel="stylesheet/scss" lang="scss">
  $bg: #ffffff;
  $light_gray: #01c176;

  /* reset element-ui css */
  .login-container {
    .el-input--suffix {
      width: 100% !important;
    }

    .login-form1,
    .login-form2 {
      .el-input__inner {
        height: 100% !important;
      }
      .el-input {
        display: inline-block;
        // height: 47px;
        height: 4.9vh;
        width: 85%;
        input {
          background: transparent !important;
          border: 0px;
          -webkit-appearance: none;
          border-radius: 0px;
          padding: 12px 5px 12px 15px;
          color: $light_gray;
          font-size: 0.729vw;
          // height: 47px;
          height: 4.9vh;
          &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0px 1000px $bg inset !important;
            -webkit-text-fill-color: $light_gray !important;
          }
        }
      }

      .el-form-item {
        border: 1px solid rgba(255, 255, 255, 0.1);
        // background: rgba(0, 0, 0, 0.1);
        // border-radius: 5px;
        color: #454545;
        border-bottom: 1px #cccccc solid;
      }
    }
    .updatePassword {
      .el-input__inner {
        height: 100% !important;
      }
      .el-input {
        display: inline-block;
        height: 5vh;
        width: 85%;
      }

      .el-form-item {
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #454545;
        margin-bottom: 20px;
      }
    }
  }
</style>
<style scoped rel="assets/disk/slidercaptcha.min.css" />
<style rel="stylesheet/scss" lang="scss" scoped>
  $bg: #2d3a4b;
  $dark_gray: #d7dee3;
  $light_gray: #01c176;

  .login-container {
    position: fixed;
    height: 100%;
    width: 100%;
    // background-color: $bg;
    // background: url("../../assets/bg.jpg") no-repeat top center/cover;
    .bg1 {
      position: fixed;
      height: 100%;
      width: 100%;
      background: #eff3fb;
      .logo2 {
        position: absolute;
        left: 7.13vw;
        top: 2.77vh;
        // width: 8.593vw;
        height: 2.06vw;
      }
      .logo3 {
        margin-left: 41px;
      }
      // background-size: contain;
      .login_form2 {
        position: absolute;
        left: 56%;
        top: 50%;
        transform: translateY(-50%);
        padding: 5.37vh 0 0 0;
        width: 32.7vw;
        height: 51.38vh;
        border-radius: 1.66vw;
        background: url('../../assets/loginFromBg2.png') no-repeat top center/cover;
      }
      .bg-img {
        width: 50.98vw;
        // height: 68.79vh;
        position: absolute;
        top: 31.2vh;
        left: 5.2vw;
      }
      // background-size: contain;

      .el-form-item {
        width: 21.45vw !important;
        height: 4.9vh !important;
        background-color: #fff !important;
        border-radius: 0.83vw !important;
        margin: 0 auto 2.71vh;
        border: none;
      }
      .title1 {
        font-size: 1.4vw;
        text-align: center;
        line-height: 3.51vh;
        font-family: AlibabaPuHuiTi_2_85_Bold;
        margin: 0 0 4.16vh;
      }
      .svg-container_login {
        width: 1.77vw !important;
        font-size: 0.93vw !important;
      }
      .login-form1 {
        position: relative;
        z-index: 99;
        // left: 0;
        // right: 0;
        // width: 520px;
        width: 100%;
        // padding: 35px 35px 15px 35px;
        // margin: 120px auto;
        // background-color: #ffffff;
      }
      .el-input {
        height: 4.9vh;
      }
    }
    .bg {
      position: fixed;
      height: 100%;
      width: 100%;
      background: #eff3fb;
      .bg-img {
        width: 50.98vw;
        // height: 68.79vh;
        position: absolute;
        top: 31.2vh;
        left: 5.2vw;
      }
      // background-size: contain;
      .login_form {
        position: absolute;
        left: 56%;
        top: 50%;
        transform: translateY(-50%);
        padding: 5.37vh 0 0 0;
        width: 32.7vw;
        height: 51.38vh;
        border-radius: 1.66vw;
        background: url('../../assets/loginFromBg.png') no-repeat top center/cover;
      }
      .logo1 {
        position: absolute;
        top: 3.51vh;
        left: 7.13vw;
        width: 8.593vw;
        // height: 2.588vw;
      }
      .el-form-item {
        width: 21.45vw !important;
        height: 4.9vh !important;
        background-color: #fff !important;
        border-radius: 0.83vw !important;
        margin: 0 auto 2.71vh;
        border: none;
      }
      .title1 {
        font-size: 1.4vw;
        text-align: center;
        line-height: 3.51vh;
        font-family: AlibabaPuHuiTi_2_85_Bold;
        margin: 0 0 4.16vh;
      }
      .svg-container_login {
        width: 1.77vw !important;
        font-size: 0.93vw !important;
      }
      .login-form1 {
        position: relative;
        z-index: 99;
        // left: 0;
        // right: 0;
        // width: 520px;
        width: 100%;
        // padding: 35px 35px 15px 35px;
        // margin: 120px auto;
        // background-color: #ffffff;
      }
      .el-input {
        height: 4.9vh;
      }
    }

    .logo {
      position: absolute;
      top: 45px;
      left: 45px;
      width: 164px;
      height: auto;
    }

    .shadow {
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      width: 19%;
      height: auto;
    }

    .login-form {
      position: relative;
      z-index: 99;
      // left: 0;
      // right: 0;
      // width: 520px;
      width: 100%;
      padding: 35px 35px 15px 35px;
      // margin: 120px auto;
      // background-color: #ffffff;
    }

    .tips {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 50px;
      display: inline-block;

      &_login {
        font-size: 20px;
      }
    }

    .title-container {
      position: relative;

      .title {
        font-size: 26px;
        font-weight: 400;
        color: $light_gray;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
      }
    }

    .show-pwd {
      position: absolute;
      // right: 10px;
      right: 1.04vw;
      // top: 7px;
      top: 1.29vh;
      font-size: 0.83vw;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .show-code {
      position: absolute;
      right: 10px;
      top: -10px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .thirdparty-button {
      position: absolute;
      right: 35px;
      bottom: 28px;
    }

    .login-btn {
      display: block;
      width: 69%;
      // margin: 40px auto;
      margin: 7.14vh auto 0.92vh;
      height: 5.74vh;
      box-sizing: border-box;
      font-size: 1.04vw;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      background: linear-gradient(180deg, #5bbea8 0%, #2e896f 100%);
      border-radius: 2.08vw;
      border: none;
      // background-color: #27cb83;
      // border-color: #27cb83;
      // border-radius: 20px;
    }
    .login-btn1 {
      display: block;
      width: 69%;
      // margin: 40px auto;
      margin: 7.14vh auto 0.92vh;
      height: 5.74vh;
      box-sizing: border-box;
      font-size: 1.04vw;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      background: linear-gradient(180deg, #1394ff 0%, #0665bf 100%);
      border-radius: 2.08vw;
      border: none;
      // background-color: #27cb83;
      // border-color: #27cb83;
      // border-radius: 20px;
    }
    .login-btn:disabled {
      background: #c8c8c8;
      border-color: #c8c8c8;
    }

    .forget {
      font-size: 0.72vw;
      text-align: center;
      color: #c0c8cd;
      cursor: pointer;
    }

    .dialog-title {
      text-align: center;
      font-weight: bold;
      color: black;
      margin-bottom: 20px;
      font-size: 16px;
    }
    .svg-container-end {
      position: absolute;
      color: $dark_gray;
      vertical-align: middle;
      display: inline-block;
      margin: 0 -30px;
      margin-top: 5px;
      font-size: 20px;
    }
    .dialog-tip {
      text-align: left;
      color: red;
      font-size: 12px;
      margin-top: 20px;
      margin-left: 50px;
    }
    .dialog-footer {
      text-align: center;
    }
  }

  @media screen and (max-width: 767px) {
    .login-container {
      // ::v-deep .verifybox {
      //   width: 100vw;
      //   .verify-bar-area {
      //     width: 94vw !important;
      //   }
      //   .verify-img-panel {
      //     width: 94vw !important;
      //   }
      // }
      ::v-deep .el-input__inner {
        height: 100% !important;
        width: 90% !important;
      }
      ::v-deep .el-input {
        // width: 70% !important;
        input {
          margin-left: 10px;

          font-size: 12px !important;
          background: transparent !important;
          // box-shadow: 0 0 0px 1000px #ffffff inset !important;
        }
      }
      ::v-deep .el-input__suffix {
        top: 3px !important;
      }
      .bg1 {
        width: 100%;
        background: url('../../assets/phoneBG1.png') no-repeat;
        background-size: cover;
        .logo2 {
          top: 9.5px;
          left: 50%;
          transform: translateX(-50%);
          height: 50px;
        }
        .logo3 {
          position: fixed;
          top: 89vh;
          left: 50%;
          transform: translateX(-50%);
          margin: 0;
        }
        .login_form2 {
          // transform: translateY(-50%);
          left: 0;
          width: 100vw;
          top: 50vh;
          transform: none;
          padding-top: 30px;
          height: 47.38vh;
          border-radius: 7.66vw;
          background: none;
        }
        .bg-img {
          width: 100vw;
          // bottom: 0;
          // bottom: 2px;
          top: 10vh;
          left: 0;
          // top: none;
        }
        .shadow {
          width: 50%;
        }
        .show-pwd {
          font-size: 14px;
          right: 31px;
        }
        .login-btn1 {
          margin: 23px auto 13px;
          height: 45px;
          width: 293px;
          // line-height: 3.74vh;
          font-size: 14px;
          border-radius: 4.08vw;
        }
        .forget {
          font-size: 14px;
          color: #c5a6a6;
        }
        .el-form-item {
          width: 293px !important;
          border-radius: 4px !important;
        }

        .title1 {
          display: none;
        }
        .svg-container {
          padding: 8px 5px 6px 11px;
          font-size: 12px !important;
        }

        .el-form-item--medium {
          ::v-deep .el-form-item__content {
            overflow: hidden;
            line-height: 20px !important;
          }
        }
        .el-select {
          width: 87% !important;
        }

        .row-bg {
          .login-form {
            padding: 35px 10px 15px 10px;
          }

          .el-input {
            width: 79%;
          }

          .el-form-item {
            margin-bottom: 10px;
          }

          .forget {
            font-size: 14px;
          }
        }

        .bottom-img {
          display: block;
          margin: 10% auto 0 auto;
        }
      }
      .bg {
        width: 100%;
        background: url('../../assets/phoneBG.png') no-repeat;
        background-size: cover;
        .logo1 {
          top: 9.5px;
          left: 50%;
          transform: translateX(-50%);
          width: 82px;
        }
        .bg-img {
          width: 100vw;
          // bottom: 0;
          // bottom: 2px;
          top: 10vh;
          left: 0;
          // top: none;
        }
        .shadow {
          width: 50%;
        }
        .show-pwd {
          font-size: 14px;
          right: 31px;
        }
        .login_form {
          // transform: translateY(-50%);
          left: 0;
          width: 100vw;
          top: 50vh;
          transform: none;
          padding-top: 30px;
          height: 47.38vh;
          border-radius: 7.66vw;
          background: none;
        }
        .login-btn {
          margin: 23px auto 13px;
          height: 45px;
          width: 293px;
          // line-height: 3.74vh;
          font-size: 14px;
          border-radius: 4.08vw;
        }
        .forget {
          font-size: 14px;
          color: #c5a6a6;
        }
        .el-form-item {
          width: 293px !important;
          border-radius: 4px !important;
        }

        .title1 {
          display: none;
        }
        .svg-container {
          padding: 8px 5px 6px 11px;
          font-size: 12px !important;
        }

        .el-form-item--medium {
          ::v-deep .el-form-item__content {
            overflow: hidden;
            line-height: 20px !important;
          }
        }
        .el-select {
          width: 87% !important;
        }

        .row-bg {
          .login-form {
            padding: 35px 10px 15px 10px;
          }

          .el-input {
            width: 79%;
          }

          .el-form-item {
            margin-bottom: 10px;
          }

          .forget {
            font-size: 14px;
          }
        }

        .bottom-img {
          display: block;
          margin: 10% auto 0 auto;
        }
      }
    }
    input[type='password']::-ms-reveal,
    input[type='password']::-ms-clear {
      display: none;
    }
  }
</style>
