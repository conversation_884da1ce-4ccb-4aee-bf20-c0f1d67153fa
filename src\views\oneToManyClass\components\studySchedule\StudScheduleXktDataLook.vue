<template>
  <div>
    <el-drawer :title="title" :visible="dataLookXktVisible" @close="handleClose" style="margin-top: 15px" :size="screenWidth > 1300 ? '35%' : '70vw'">
      <el-row v-loading="loading" class="paikeTwo" style="margin-top: 1vw">
        <el-col class="paike">班级名称：{{ classInfo.className }}</el-col>
        <el-col class="paike">班级编号：{{ classInfo.classCode }}</el-col>
        <el-col class="paike">年级：{{ classInfo.grade }}</el-col>
        <el-col class="paike">日期：{{ classInfo.date }}</el-col>
        <el-col class="paike">上课用时：{{ classInfo.actualTime }}</el-col>
        <el-col class="paike">学段：{{ classInfo.xktGradeName }}</el-col>
        <el-col class="paike">
          授课视频:
          <el-row :gutter="20" v-for="(row, rowIndex) in videoGroups" :key="'video-row-' + rowIndex">
            <el-col :span="24" v-for="(video, colIndex) in row" :key="'video-col-' + rowIndex + '-' + colIndex">
              <el-card class="video-card">
                <div class="video-name">{{ video.videoName }}</div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col class="paike">
          <span>课程名称:</span>
          <el-row :gutter="20" v-for="(name, index) in normalizedCourseNames" :key="'course-row-' + index">
            <el-col :span="24">
              <el-card class="video-card">
                <div class="card-content">
                  <span class="course-name">{{ name }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col class="courseStudent">
          <span>课程学员:</span>
          <el-row style="margin-top: 10px" :gutter="20">
            <el-col :span="6" v-for="(student, index) in studentsList" :key="student.studentCode" style="margin-bottom: 15px">
              <el-radio-group v-model="selectedStudent" @input="handleStudentClick(index)">
                <el-radio :label="student.studentCode" style="width: 100%; position: relative" border class="student-radio" @input="handleStudentClick(student, index)">
                  {{ student.studentName }}
                  <!-- 缺课标记 - 直接覆盖在名称上 -->
                  <div v-if="student.isAbsent == 1" class="absent-overlay">缺课</div>
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div class="dataDetails">
        <el-row v-loading="loading">
          <el-col>
            <p class="title2">学习准备</p>
            <p class="title3">1.预习任务完成</p>
            <el-radio-group v-model="form.radio" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col>
            <p class="title3">2.进门测试题目正确率</p>
            <el-radio-group v-model="form.accuracy" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-input class="custom-textarea" type="textarea" placeholder="备注（详细记录），请输入......" v-model="form.notes" maxlength="30" disabled show-word-limit></el-input>
          <el-col style="margin-top: 20px">
            <p class="title2">注意力集中度</p>
            <p class="title3">1.眼神跟随教师/屏幕</p>
            <el-radio-group v-model="form.follow" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col>
            <p class="title3">2.无关操作{{ '<=' }}2次 /节课</p>
            <el-radio-group v-model="form.operate" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col style="margin-top: 20px">
            <p class="title2">课堂参与度</p>
            <p class="title3">1.主动回答问题>=1次/节课</p>
            <el-radio-group v-model="form.participation" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col>
            <p class="title3">2.课后错题讲解+知识点回顾质量</p>
            <el-radio-group v-model="form.quality" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col style="margin-top: 20px">
            <p class="title2">精彩时刻</p>
            <p class="title3">1.听课状态</p>
            <el-radio-group v-model="form.state" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col style="margin-top: 20px">
            <p class="title2">情绪与态度</p>
            <p class="title3">1.学习积极性</p>
            <el-radio-group v-model="form.enthusiasm" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col>
            <p class="title3">2.是否有厌学情绪，抵触心理</p>
            <el-radio-group v-model="form.conflict" style="display: flex; justify-content: space-between" disabled>
              <el-radio :label="1">√</el-radio>
              <el-radio :label="2">×</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-col>
          <el-col style="margin-top: 20px">
            <p class="title2">教练评语</p>
            <el-input class="custom-textarea" disabled type="textarea" placeholder="请输入......" v-model="form.comment" maxlength="200" show-word-limit></el-input>
            <!-- <div style="display: flex; justify-content: center; margin-top: 20px">
              <el-button type="success" @click="handleClose">确定</el-button>
            </div> -->
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>
<script>
  export default {
    name: 'DataLookXktDialog',
    data() {
      return {
        title: '数据查看',
        dataLookXktVisible: false,
        loading: false,
        screenWidth: document.documentElement.clientWidth,
        selectedVideo: '',
        classInfo: {
          className: '',
          classCode: '',
          grade: '',
          date: '',
          actualTime: '',
          xktGradeName: ''
        },
        videoList: [],
        xktCourseName: null, // 可以是任意长度的数组
        selectedStudent: null, // 存储选中的学员ID
        studentsList: [], // 学生列表
        form: {
          radio: '',
          accuracy: '',
          notes: '',
          follow: '',
          operate: '',
          participation: '',
          quality: '',
          state: '',
          enthusiasm: '',
          conflict: '',
          comment: ''
        }
      };
    },
    methods: {
      show(loading) {
        this.dataLookXktVisible = true;
        this.loading = loading;
      },
      loadData(data, loading) {
        this.loading = loading;
        console.log('data', data);
        this.classInfo.className = data.className;
        this.classInfo.classCode = data.classCode;
        this.classInfo.grade = data.grade;
        this.classInfo.date = data.date;
        this.classInfo.actualTime = data.actualTime;
        this.classInfo.xktGradeName = data.xktCourseStatisticsDto.xktGradeName;
        this.videoList = data.xktCourseStatisticsDto.courseGradeVideos;
        this.xktCourseName = data.xktCourseStatisticsDto.xktCourseName;
        this.studentsList = data.feedbackTableList;
        this.selectedStudent = this.studentsList[0].studentCode; // 默认选中第一个学生
        // 初始化第一个学生数据
        this.form.radio = this.studentsList[0].preparationCompleted;
        this.form.accuracy = this.studentsList[0].entryTestAccuracy;
        this.form.notes = this.studentsList[0].entryTestRate;
        this.form.follow = this.studentsList[0].eyeFollowingScore;
        this.form.operate = this.studentsList[0].irrelevantActions;
        this.form.participation = this.studentsList[0].activeAnswers;
        this.form.quality = this.studentsList[0].mistakeKnowledgeReviewQuality;
        this.form.state = this.studentsList[0].learningStatus;
        this.form.enthusiasm = this.studentsList[0].learningEngagement;
        this.form.conflict = this.studentsList[0].resistanceSigns;
        this.form.comment = this.studentsList[0].feedback;
      },
      closeDrawer() {
        this.dataLookXktVisible = false;
      },
      handleResize() {
        this.screenWidth = document.documentElement.clientWidth;
      },
      handleClose() {
        this.dataLookXktVisible = false;
      },
      handleStudentClick(index) {
        console.log(this.studentsList[index]);

        this.form.radio = this.studentsList[index].preparationCompleted;
        this.form.accuracy = this.studentsList[index].entryTestAccuracy;
        this.form.notes = this.studentsList[index].entryTestRate;
        this.form.follow = this.studentsList[index].eyeFollowingScore;
        this.form.operate = this.studentsList[index].irrelevantActions;
        this.form.participation = this.studentsList[index].activeAnswers;
        this.form.quality = this.studentsList[index].mistakeKnowledgeReviewQuality;
        this.form.state = this.studentsList[index].learningStatus;
        this.form.enthusiasm = this.studentsList[index].learningEngagement;
        this.form.conflict = this.studentsList[index].resistanceSigns;
        this.form.comment = this.studentsList[index].feedback;
      }
    },
    mounted() {
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    computed: {
      videoGroups() {
        const groups = [];
        const videos = this.videoList || [];
        for (let i = 0; i < videos.length; i += 2) {
          groups.push(videos.slice(i, i + 2));
        }
        return groups;
      },
      normalizedCourseNames() {
        const rawValue = this.xktCourseName;

        // 空值处理
        if (rawValue === null || rawValue === undefined) {
          return [];
        }

        // 已是数组直接返回
        if (Array.isArray(rawValue)) {
          return rawValue;
        }

        // 字符串自动分割
        if (typeof rawValue === 'string') {
          return rawValue
            .split(/[,，;]/)
            .map((name) => name.trim())
            .filter(Boolean);
        }

        // 其他类型统一包装
        return [rawValue];
      }
    }
  };
</script>

<style scoped>
  .paike {
    margin-bottom: 15px;
    font-size: 16px;
  }

  .paikeTwo {
    padding: 0 20px;
  }

  ::v-deep .el-drawer__header > :first-child {
    text-align: center;
    font-weight: 1000;
    color: black;
  }

  ::v-deep .courseName .el-input__inner {
    width: 100%;
    margin-bottom: 10px;
  }
  .video-card {
    margin: 10px 0;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  .centered-input {
    ::v-deep .el-input__inner {
      text-align: center;
      padding: 14px 16px;
      font-size: 15px;
      border-radius: 10px;
      border: 1px solid #e0e0e0;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
      }
    }
  }
  .student-radio {
    display: block;
    text-align: center;
    position: relative;
    /* 为覆盖标记提供定位上下文 */
  }

  /* 覆盖在名称上的缺课标记 */
  .absent-overlay {
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(245, 108, 108, 0.9);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 0 4px 0 10px;
    /* 只圆角右上和右下 */
    z-index: 10;
    font-weight: bold;
    transform: translate(30%, -30%) rotate(15deg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* 不可选状态样式 */
  .el-radio.is-disabled {
    cursor: not-allowed;
    opacity: 0.8;
    background-color: #f5f5f5;
  }

  .el-radio.is-disabled .el-radio__label {
    color: #909399;
    position: relative;
    /* 确保名称在覆盖标记下方 */
    z-index: 1;
    /* 防止覆盖标记遮挡文本 */
  }

  /* 选中状态样式 */
  ::v-deep .el-radio.is-checked[data-v-78b55c70]:not(.is-disabled) {
    border-color: #409eff;
    background-color: #f0f7ff !important;
  }

  .dataDetails {
    padding: 20px;
    margin-left: 20px;
    margin-right: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .title2 {
    margin: 0;
    padding: 8px;
    display: inline-block;
    border-radius: 5px;
    background-color: #409eff;
    color: white;
  }

  .title3 {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .custom-textarea ::v-deep .el-textarea__inner {
    margin-top: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
  }

  ::v-deep .el-textarea .el-input__count {
    background-color: #f5f5f5;
  }

  ::v-deep .el-radio.is-checked[data-v-7ba5bd90]:not(.is-disabled) {
    background-color: #fff;
  }
  /* 自定义禁用状态下选中项的样式 */
  ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
    border-color: #409eff !important;
    background: #1890ff;
  }

  /* 可选：调整悬停效果，保持一致性 */
  ::v-deep .el-radio__input.is-checked.is-disabled:hover {
    background-color: #337ecc !important; /* 悬停时的高亮颜色 */
    border-color: #337ecc !important;
  }

  ::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    background-color: #fff !important;
  }

  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: #337ecc !important;
  }
  ::v-deep .el-radio.is-disabled[data-v-78b55c70] {
    background-color: #fff !important;
  }
</style>
