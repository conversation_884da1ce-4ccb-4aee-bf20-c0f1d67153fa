<template>
  <div>
    <el-dialog title="补课单" width="700px" :visible.sync="dialogVisible" destroy-on-close append-to-body :close-on-click-modal="false">
      <div v-loading="loading">
        <!-- 学员标签页（仅type=5且有多学员时显示） -->
        <el-row v-if="type === '5' && detailData.studentList.length > 1">
          <el-tabs v-model="studentListIndex">
            <el-tab-pane v-for="(item, index) in detailData.studentList" :key="index" :label="item.studentName" :name="String(index)" />
          </el-tabs>
        </el-row>

        <el-form ref="detailForm" :model="detailData" label-position="right" label-width="110px" class="detail-form">
          <el-form-item label="课程类型：">
            <el-input v-model="detailData.curriculumName" disabled />
          </el-form-item>
          <el-form-item label="补课人姓名：">
            <el-input v-model="detailData.studentName" disabled />
          </el-form-item>
          <el-form-item label="补课人手机号：">
            <el-input v-model="detailData.phone" disabled />
          </el-form-item>
          <el-form-item label="补课人年级：" prop="grade">
            <el-select v-model="detailData.grade" placeholder="" multiple disabled style="width: 100%">
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学员性别：">
            <el-input v-model="detailData.gender" disabled />
          </el-form-item>
          <el-form-item label="请假课程时间：">
            <el-input v-model="detailData.courseTimeOff" disabled />
          </el-form-item>
          <el-form-item label="补课时间：">
            <el-input v-model="detailData.makeUpClassTime" disabled />
          </el-form-item>
          <el-form-item label="学员所在区域：">
            <el-input v-model="detailData.address" disabled />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getLessonsList } from '@/api/oneToManyClass/pendingCompletionClassInfo';

  export default {
    name: 'MakeUpClassDetailDialog',
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      detailInfo: {
        type: Object,
        default: () => ({})
      },
      selectedStudents: {
        type: Array,
        default: () => []
      },
      type: {
        //补课-派单中 '5' 补课-等待成班中'6'
        type: String,
        default: ''
      },
      gradeList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        detailData: {
          curriculumName: '',
          studentName: '',
          phone: '',
          gradeLabel: '',
          gender: '',
          makeUpClassTime: '', // 补课
          courseTimeOff: '', // 请假课程时间
          address: '',
          province: '',
          city: '',
          area: '',
          studentCodes: [],
          classStudentLeaveIds: [],
          studentList: [] // 新增学员列表
        },
        _isMounted: false,
        studentListIndex: '0',
        loading: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('change', val);
        }
      }
    },
    watch: {
      dialogVisible(val) {
        if (val && this._isMounted) {
          this.initDetailData();
        }
      },
      studentListIndex(newVal) {
        if (this.type === '5' && this.detailData.studentList.length > 0) {
          this.updateCurrentStudent(parseInt(newVal));
        }
      }
    },
    mounted() {
      this._isMounted = true;
    },
    methods: {
      initDetailData() {
        const detailInfo = this.detailInfo || {};
        // 初始化学员列表
        const studentList = this.type === '5' ? detailInfo.studentList : [];
        const classStudentLeaveIds = studentList.map((item) => item.studentLeaveId);
        console.log(detailInfo, '////detailInfo');
        const gradeValues = (() => {
          const gradeStr = detailInfo.grade ?? '';
          if (this.type === '5') {
            return gradeStr
              .split(',')
              .map((v) => v.trim())
              .filter((v) => v !== ''); // 更明确的空值过滤
          }
          return gradeStr !== '' ? [String(gradeStr)] : [];
        })();
        this.detailData = {
          ...this.detailData,
          studentList,
          grade: gradeValues,
          curriculumName: detailInfo.curriculumName || detailInfo.courseName,
          courseName: detailInfo.courseName || '',
          studentName: detailInfo.studentName || '',
          phone: detailInfo.phone || '',
          makeUpClassTime: detailInfo.makeUpClassTime || '',
          studentCode: detailInfo.studentCode || '',
          id: detailInfo.id || '',
          studentCodes: detailInfo.studentCode ? [detailInfo.studentCode] : [],
          classStudentLeaveIds: this.type === '5' ? classStudentLeaveIds : detailInfo.id ? [detailInfo.id] : []
        };
        // type=5时初始化第一个学员数据
        if (this.type === '5' && studentList.length > 0) {
          this.updateCurrentStudent(0);
        } else {
          this.getInfo();
        }
      },

      updateCurrentStudent(index) {
        const student = this.detailData.studentList[index];
        if (!student) return;
        // 更新当前显示的学生信息
        this.detailData.studentName = student.studentName || '';
        this.detailData.studentCode = student.studentCode || '';
        this.detailData.id = student.id || '';
        // 设置查询参数
        this.detailData.studentCodes = student.studentCode ? [student.studentCode] : [];
        this.detailData.classStudentLeaveIds = student.studentLeaveId ? [student.studentLeaveId] : [];

        // 获取学员详情
        this.getInfo();
      },

      async getInfo() {
        this.loading = true;
        try {
          const params = {
            studentCodes: this.detailData.studentCodes,
            classStudentLeaveIds: this.detailData.classStudentLeaveIds
          };
          const res = await getLessonsList(params);
          const studentData = res.data?.[0] ?? {};
          const { phone = '', address = '', gender, courseTimeOff = '', makeUpClassTime = '' } = studentData;
          this.detailData.gender = this.formatGender(gender);
          this.detailData.courseTimeOff = courseTimeOff;
          this.detailData.makeUpClassTime = makeUpClassTime;
          this.detailData.phone = this.detailInfo.phone ? this.detailInfo.phone : phone;
          this.detailData.address = address;
        } catch (e) {
          console.error('获取学员信息失败:', e);
        } finally {
          this.loading = false;
        }
      },
      // 处理id找

      formatGender(genderCode) {
        const genderMap = {
          0: '男',
          1: '女'
        };
        return genderMap[genderCode] || '';
      }
    }
  };
</script>

<style scoped lang="scss">
  .detail-form {
    padding-right: 10px;
  }
</style>
