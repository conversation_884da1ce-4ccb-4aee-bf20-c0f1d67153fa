// 班级上课时间管理 相关接口
import request from '@/utils/request';

// 获取1v多课程大类
export const getOneToMoreClassList = () => {
  return request({
    url: '/znyy/curriculum/allNew',
    method: 'GET',
    params: {
      curriculumType: 1
    }
  });
};
/**
 * 交付中心交付小组配置-获取交付小组列表
 * @param data
 */
export const getTeamList = (data) => {
  return request({
    url: '/deliver/web/team/list/page',
    method: 'GET',
    params: data
  });
};
// 搜索交付中心
export const searchDeliverCenter = (params) =>
  request({
    url: '/deliver/web/deliverClass/getAllMerchant',
    method: 'post',
    params
  });

// 根据交付中心编号、课程大类id、年级及课程分类获取所有未满班级列表数据
export const getNotFullClassList = (params) =>
  request({
    url: '/deliver/web/oneMore/getDeliverClassByCurriculumGrade',
    method: 'get',
    params: { teacherType: '', ...params }
  });

// 一对多班级列表-等待成班中-指派班级
export const assignClass = (data) =>
  request({
    url: '/deliver/web/oneMore/changeStudentListToClass',
    method: 'post',
    data
  });

// 一对多班级列表-等待成班中-创建班级
export const createClass = (data, type) =>
  request({
    url: '/deliver/web/oneMore/addClassWithStudents',
    method: 'post',
    data: {
      type,
      ...data
    }
  });

// 根据交付中心查询班级列表
export const getTemporaryClassListByMerchantCode = (params) =>
  request({
    url: '/deliver/temporary/class/getTemporaryClassListByMerchantCode',
    method: 'get',
    params
  });

//补课班指派班级;
export const assignClasses = (data) =>
  request({
    url: '/deliver/temporary/class/assignClasses',
    method: 'post',
    data
  });

// 班级类型枚举列表
export const getClassTypeEnum = () =>
  request({
    url: '/deliver/bvStatus/getClassConfig',
    method: 'get'
  })
