/**
 * 一对多班级列表相关接口
 */
import request from '@/utils/request';
/**
 * 获取班级列表数据
 * @param {string} pageNum 当前页
 * @param {string} pageSize 每页条数
 * @param {object} data 查询条件
 * @returns
 */
export function getClassListData(data) {
  return request({
    url: '/deliver/web/deliverClass/getDeliverClassPage',
    method: 'GET',
    params: data
  });
}
/**
 * 获取所有一对多课程大类
 * @param {*} data
 * @returns
 */
export const getAllOneToManyCurriculumType = (data) => {
  return request({
    url: '/znyy/curriculum/allNew',
    method: 'GET',
    params: { ...data, curriculumType: 1 }
  });
};
/**
 * 获取所有交付中心
 * @returns
 */
export function getAllDeliver(data) {
  return request({
    url: '/deliver/web/deliverClass/getAllMerchant',
    method: 'POST',
    params: data
  });
}
/**
 * 通过课程大类、交付中心商户号及课程分类获取上课教练及代课教练数据
 * @param {*} curriculumId 课程大类
 * @param {*} merchantCode 交付中心商户号
 * @param {*} type 课程分类
 * @returns
 */
export function getTeacherListByCurriculumIdAndMerchantCode(curriculumId, merchantCode, type) {
  return request({
    url: '/deliver/web/deliverClass/getTeacherByMerchant',
    method: 'POST',
    params: { curriculumId, merchantCode, type }
  });
}
/**
 * 新增班级数据
 * @param {*} data
 * @returns
 */
export function setClassAddData(data) {
  return request({
    url: '/deliver/web/deliverClass/addDeliverClass',
    method: 'POST',
    data
  });
}
/**
 * 编辑班级数据
 * @param {object} data
 * @returns
 */
export function setClassUpdateData(data) {
  return request({
    url: '/deliver/web/deliverClass/updateDeliverClass',
    method: 'POST',
    data
  });
}
/**
 * 编辑补课班级数据
 * @param {object} data
 * @returns
 */
export function updateTemporaryClass(data) {
  return request({
    url: '/deliver/web/deliverClass/updateTemporaryClass',
    method: 'POST',
    data
  });
}
/**
 * 根据班级id获取排课详情数据
 * @param {string} classId
 * @returns
 */
export function getCourseSchedulingDetail(classId) {
  return request({
    url: '/deliver/web/deliverClass/getClassPlanStudyByClassId',
    method: 'POST',
    params: { classId }
  });
}
/**
 * 根据班级id获取上课老师列表数据
 * @param {*} id
 * @returns
 */
export function getCourseSchedulingTeacherList(deliverClassId) {
  return request({
    url: '/deliver/web/oneMore/getTeacherList',
    method: 'GET',
    params: { deliverClassId }
  });
}
/**
 * 根据班级id获取上课老师列表数据(新)
 * @param {*} id
 * @returns
 */
export function getNewTeacherList(temporaryClassId) {
  return request({
    url: '/deliver/web/oneMore/getNewTeacherList',
    method: 'GET',
    params: { temporaryClassId }
  });
}
/**
 * 设置班级排课数据
 * @param {*} data
 * @param {*} type 分类（1、试课 2、正式课）
 * @returns
 */
export function setCourseSchedulingData(data, type) {
  let url = '/deliver/web/oneMore/addOneMoreClassDeliverCourse';
  if (type == 1) {
    url = '/deliver/web/oneMore/addOneMoreClassExperienceCourse';
  }
  return request({
    url,
    method: 'POST',
    data
  });
}
// 补课排课保存
export function addTempOrAryOneMoreClass(data) {
  return request({
    url: '/deliver/web/oneMore/addTempOrAryOneMoreClass',
    method: 'POST',
    data
  });
}
export function getHistory(classId) {
  return request({
    url: '/deliver/web/deliverClass/getClassDispatchHistory',
    method: 'GET',
    params: { classId }
  });
}
/**
 * 删除班级数据
 * @param {string} id
 */
export function setDeleteClassData(id) {
  return request({
    url: '/deliver/web/deliverClass/delDeliverClassById',
    method: 'POST',
    params: { id }
  });
}
/**
 * 补课班级列表分页查询
 * @param {string} pageNum 当前页
 * @param {string} pageSize 每页条数
 * @param {object} data 查询条件
 * @returns
 */
export function getTemporaryClassList(data) {
  return request({
    url: '/deliver/web/deliverClass/getTemporaryClassList',
    method: 'GET',
    params: data
  });
}

/**
 * 补课班级列表删除接口
 * @param {string} id
 */
export const delTemporaryClassById = (id) => {
  return request({
    url: '/deliver/web/deliverClass/delTemporaryClassById',
    method: 'delete',
    params: { id }
  });
};
