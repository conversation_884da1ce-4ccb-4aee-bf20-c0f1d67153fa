<!-- 一对多-学生列表-上课信息对接表 -->
<template>
  <!-- 上课信息对接表 -->
  <el-dialog title="上课信息对接表" :visible.sync="classConnectionDialogVisible" :width="screenWidth > 1300 ? '60%' : '90%'" :close-on-click-modal="false" center>
    <div style="margin-bottom: 40px; margin-top: -30px; font-size: 15px">
      <span>创建时间：{{ classConnectionData.submitTime ? classConnectionData.submitTime : classConnectionData.updateTime }}</span>
    </div>
    <el-form ref="classConnectionData" :model="classConnectionData" :label-width="screenWidth > 1300 ? '150px' : '70px'">
      <el-form-item label="学员姓名" prop="studentName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.studentName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="学员编号" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.studentCode }}</span>
        </div>
      </el-form-item>
      <el-form-item label="联系方式" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.mobile }}</span>
        </div>
      </el-form-item>
      <el-form-item label="课程类型" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.curriculumName }}</span>
        </div>
      </el-form-item>
      <!-- <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.referrerPhone ? classConnectionData.referrerPhone : '无' }}</span>
        </div>
      </el-form-item> -->
      <div v-if="classConnectionData.curriculumCode === 'MATH'">
        <el-form-item label="版本" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.extendFieldList[0].fieldValue }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学科" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.extendFieldList[1].fieldValue }}</span>
          </div>
        </el-form-item>
        <el-form-item label="当前年级" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ classConnectionData.extendFieldList[2].fieldValue }}</span>
          </div>
        </el-form-item>
      </div>
      <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ getGrade(classConnectionData.grade) }}</span>
        </div>
      </el-form-item>
      <el-form-item label="充值学时" prop="rechargeHour" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.rechargeHour }}</span>
        </div>
      </el-form-item>
      <el-form-item label="上课时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-form-item label="" v-for="(val, index) in classConnectionData.studyTimeList" :key="index" style="margin: 0">
          <div class="timeClass" style="margin: 0 0 18px">
            <span style="margin: 0 15px">{{ val.weeks }}</span>
          </div>
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ val.startTime }}</span>
            <span>至</span>
            <span style="margin: 0 15px">{{ val.endTime }}</span>
          </div>
        </el-form-item>
      </el-form-item>
      <div style="position: relative; display: flex; margin-bottom: 22px; align-items: center" v-if="classConnectionData.firstTime">
        <div :style="{ width: screenWidth > 1300 ? '150px' : '85px' }" style="text-align: end; padding-right: 12px">
          首次上课时间
          <div style="color: #999999; font-size: 11px">限制24小时之后</div>
        </div>
        <div class="timeClass" style="margin-left: 0; line-height: 36px" :style="{ width: screenWidth > 1300 ? '36%' : '100%' }">
          <span style="margin: 0 15px">{{ getFormatToService(classConnectionData.firstTime, classConnectionData.firstWeek) }}</span>
        </div>
      </div>
      <el-form-item label="是否试课" prop="isExp" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.isExp == 1 ? '是' : '否' }}</span>
        </div>
      </el-form-item>
      <el-form-item label="是否新生" prop="isNewStudent" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ classConnectionData.isNewStudent == 1 ? '是' : '否' }}</span>
        </div>
      </el-form-item>
      <div v-if="xktDialogVisible === true">
        <el-form-item label="联系人收货地址" prop="address" style="width: 70%">
          <el-input v-model="classConnectionData.extendFieldList[2].fieldValue" type="textarea" placeholder="请输入" :rows="5" maxlength="200" />
        </el-form-item>
        <el-form-item label="学段：" prop="gradeLevel">
          <el-select v-model="classConnectionData.extendFieldList[0].fieldValue" clearable>
            <el-option v-for="item in period" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考试时间：" prop="gradeLevel">
          <el-date-picker v-model="classConnectionData.extendFieldList[1].fieldValue" type="date" placeholder="选择日期"></el-date-picker>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 20px">{{ classConnectionData.remark }}</span>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="classConnectionDialogVisible = false">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import dayjs from 'dayjs';
  export default {
    name: 'StudentListClassConnectionDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      valueData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        screenWidth: window.screen.width,
        reviewWeekList: [],
        gradeNameArr: [
          '一年级',
          '二年级',
          '三年级',
          '四年级',
          '五年级',
          '六年级',
          '初一',
          '初二',
          '初三',
          '高一',
          '高二',
          '高三',
          '大一',
          '大二',
          '大三',
          '大四',
          '其他',
          '幼儿园'
        ],
        normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),
        period: [
          { value: 2, label: '高中' },
          { value: 1, label: '初中' }
        ],
        code: [
          'XKT_CZHXN',
          'XKT_CZSXN',
          'XKT_CZWLN',
          'XKT_CZYWN',
          'XKT_CZYYN',
          'XKT_GZDLN',
          'XKT_GZHXN',
          'XKT_GZLSN',
          'XKT_GZSWN',
          'XKT_GZSXN',
          'XKT_GZWLN',
          'XKT_GZYWN',
          'XKT_GZYYN',
          'XKT_GZZZN'
        ],
        xktDialogVisible: false
      };
    },
    computed: {
      classConnectionDialogVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      },
      classConnectionData: {
        get() {
          console.log('this.reviewWeekList', this.valueData);
          if (this.code.includes(this.valueData.curriculumCode)) {
            this.xktDialogVisible = true;
          } else {
            this.xktDialogVisible = false;
          }
          this.reviewWeekList = this.valueData.reviewWeek ? JSON.parse(this.valueData.reviewWeek) : [];
          this.reviewWeekList.sort((a, b) => a - b);

          // 处理上课时间格式化
          let studyTimeList = [];
          if (Array.isArray(this.valueData.studyTimeList)) {
            this.valueData.studyTimeList.forEach((each) => {
              let index = studyTimeList.findIndex((item) => item.startTime === each.startTime && item.endTime === each.endTime);
              if (index > -1) {
                studyTimeList[index].weeks += `、${this.getWeekName(each.usableWeek)}`;
              } else {
                studyTimeList.push({
                  weeks: this.getWeekName(each.usableWeek),
                  startTime: each.startTime,
                  endTime: each.endTime
                });
              }
            });
          }
          return { ...this.valueData, studyTimeList };
        }
      }
    },
    methods: {
      getGrade(val) {
        return this.gradeNameArr[val - 1];
      },
      getWeekName(week) {
        return this.normalWeekData[Number(week) - 1];
      },
      getFormatToService(date, week) {
        if (date) {
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
          } else {
            allStr = str.replace('&', '');
          }
          return allStr;
        }
        return '-';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
    .vocabulary {
      position: absolute;
      top: 6px;
      right: -1px;
      height: 24px;
      color: #fff;
      font-size: 12px;
      line-height: 24px;
      border-radius: 3px;
      padding: 0 4px;
      background-color: #46a6ff;
    }
  }

  .week {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 7px 20px;
  }
</style>
