import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
import store from '@/store';
import { getToken } from '@/utils/auth';
import Code from '@/utils/code';
import { baseUrl } from './constants';
// create an axios instance
const service = axios.create({
  baseURL: baseUrl, // api的base_url
  timeout: 60000, // request timeout
  withCredentials: true // 使前台能够保存cookie
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    // 动态设置 baseURL
    const host = window.location.host;
    const subdomain = host.split('.')[0];
    const isLocalhost = /^([0-9.]+|localhost):[0-9]+$/.test(host);
    const isNgrokDomain = host.includes('ngrok');
    const isTestDomain = host.includes('test');
    const isDeliverDomain = subdomain.includes('deliver');
    if (!isLocalhost && !isNgrokDomain && !isTestDomain) {
      // 非本地环境、非 ngrok 环境且非 deliver 相关域名，使用自定义域名
      if (!isDeliverDomain) {
        const prefix = subdomain.slice(0, -1);
        config.baseURL = `https://${prefix}i.dxznjy.com/`;
      }
    }
    if (
      config.url.includes('zx/exp/pageExperienceOrder') ||
      config.url.includes('zx/exp/saveDelive') ||
      config.url.includes('zx/exp/getInfo') ||
      config.url.includes('zxAdminCourse')
    ) {
      ///2024/2/2 待完善试课信息表接口返回登录失败修改
      config.headers['Token'] = getToken();
    }
    if (store.getters.token) {
      config.headers['x-www-iap-assertion'] = getToken(); // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    }
    config.headers['www-cid'] = 'dx_deliver_resource';
    config.headers['dx-source'] = 'DELIVER##BROWSER##WEB';
    config.headers['temp-dx-source'] = 'DELIVER##BROWSER##WEB';
    config.headers['Content-Type'] = 'application/json;charset=UTF-8';
    /// 获取组织成员树状列表不传token
    if (config.url.includes('/scrm/qywechat/getUserTree')) {
      delete config.headers['Token'];
      delete config.headers['x-www-iap-assertion'];
    }
    if (config.url.includes('znyy/sys/user/getLoginPhone')) {
      config.headers['www-cid'] = 'dx_znyy_resource';
      config.headers['dx-source'] = 'ZNYY##BROWSER##WEB';
      config.headers['temp-dx-source'] = 'ZNYY##BROWSER##WEB';
    }
    return config;
  },
  (error) => {
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);
// respone interceptor
service.interceptors.response.use(
  /**
   * 下面的注释为通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
   * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
   */
  (res) => {
    if (res.data.type === 'application/octet-stream') {
      return res.data;
    }
    if (res.data.succ || res.data.success) {
      //console.log(JSON.stringify(res.data)+"wyy");

      // 如果后台返回的json显示成功，pass
      return res.data;
    } else {
      if (res.data.code == Code.UNAUTHEN || res.data.code == Code.SESSION_TIMOUT || res.data.code == 40008 || res.code == 50004) {
        // 处理登录相关的错误
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload(); // 为了重新实例化vue-router对象 避免bug
          });
        });
      } else {
        // 其它错误弹出错误信息
        // return Promise.reject(new Error(res.message || 'Error'))
        // console.log(res);
        if (res.data.code === 80001) {
          return Promise.reject(res.data);
        } else if (res.data.code === 50057) {
          MessageBox.alert(res.data.message, {
            confirmButtonText: '确定'
          });
          // Message({ message: res.data.message, type: "warning", duration: 2000 });
        } else {
          Message({ message: res.data.message, type: 'error', duration: 2000 });
        }
      }
      return Promise.reject('error');
    }
  },

  /**
   * 请求发生错误，一般都是服务器抛异常了
   */
  (err) => {
    if (err && err.response) {
      console.log(err);
      console.log(err.response.data);

      if (err.response.data.code == 50004) {
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload(); // 为了重新实例化vue-router对象 避免bug
          });
        });
      }

      if (err.response.data.message) {
        switch (err.response.status) {
          case 400:
            err.message = err.response.data.message;
            break;
          case 401:
            err.message = err.response.data.message;
            break;
          case 403:
            err.message = err.response.data.message;
            break;
          case 404:
            err.message = err.response.data.message;
            break;
          case 408:
            err.message = err.response.data.message;
            break;
          case 500:
            err.message = err.response.data.message;
            break;
          case 501:
            err.message = err.response.data.message;
            break;
          case 502:
            err.message = err.response.data.message;
            break;
          case 503:
            err.message = err.response.data.message;
            break;
          case 504:
            err.message = err.response.data.message;
            break;
          case 505:
            err.message = err.response.data.message;
            break;
          default:
            err.message = `连接出错(${err.response.status})!`;
        }
      } else {
        switch (err.response.status) {
          case 400:
            err.message = '请求错误(400)';
            break;
          case 401:
            err.message = '未授权，请重新登录(401)';
            break;
          case 403:
            err.message = '账户没有权限';
            break;
          case 404:
            err.message = '请求出错(404)';
            break;
          case 408:
            err.message = '请求出错(408)';
            break;
          case 500:
            err.message = '服务器错误';
            break;
          case 501:
            err.message = '服务未实现(501)';
            break;
          case 502:
            err.message = '网络错误(502)';
            break;
          case 503:
            err.message = '服务不可用(503)';
            break;
          case 504:
            err.message = '网络超时(504)';
            break;
          case 505:
            err.message = 'HTTP版本不受支持(505)';
            break;
          case 80000:
            err.message = '147258369';
            break;
          default:
            err.message = `连接出错(${err.response.status})!`;
        }
      }
    } else {
      err.message = '连接服务器失败!';
    }
    if (!err.response.data.message) {
      try {
        const reader = new FileReader();
        reader.onload = function (event) {
          const mymessage = JSON.parse(reader.result).message;
          if (mymessage) {
            Message({ message: mymessage, type: 'error', duration: 3000 });
          } else {
            Message({ message: err.message, type: 'error', duration: 3000 });
          }
        };
        reader.readAsText(err.response.data);
      } catch (e) {
        Message({ message: err.message, type: 'error', duration: 3000 });
      }
    } else {
      Message({ message: err.message, type: 'error', duration: 3000 });
    }
    return Promise.reject('error');
  }
);

export default service;
