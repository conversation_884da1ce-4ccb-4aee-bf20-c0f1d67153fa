/** 分页查询时，后台返回的数据中分页参数的命名，返回示例：
 *
 * {
 *
 * "msg":"ok",
 * "code":1,
 * "succ":true,
 * "oper":"default",
 * "page":{yarn
 *          "current":1,
 *          "pages":2,
 *          "records":[],
 *          "size":3,
 *          "total":5
 *        }
 * }
 * */
export const pageParamNames = ['currentPage', 'totalPage', 'totalItems'];
export const pageParamNamestwo = ['currentPage', 'totalPage', 'totalItems'];

export const permType = {
  MENU: 1,
  BUTTON: 2,
  API: 3
};
export const dxSource = 'DELIVER##BROWSER##WEB';
export const tempDxSource = 'DELIVER##BROWSER##WEB';
/**
 * 下拉选择框数据：权限类型
 *
 */
export const permTypeOptions = [
  { value: permType.MENU, label: '菜单' },
  { value: permType.BUTTON, label: '按钮' },
  { value: permType.API, label: '接口' }
];

/**
 * 权限类型
 * @type {Map<any, any>}
 */
export const permTypeMap = new Map([
  [permType.MENU, '菜单'],
  [permType.BUTTON, '按钮'],
  [permType.API, '测试']
]);

export const confirm = {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning'
};

export const XKTCurriculumCodeArr = [
  'XKT',
  'XKT_CZHX1',
  'XKT_CZSX1',
  'XKT_CZWL1',
  'XKT_CZYW1',
  'XKT_CZYY1',
  'XKT_GZDL',
  'XKT_GZHX',
  'XKT_GZLS',
  'XKT_GZSW',
  'XKT_GZSX',
  'XKT_GZWL',
  'XKT_GZYW',
  'XKT_GZYY',
  'XKT_GZZZ'
]; //学考通相关课程大类
export const XSMCurriculumCodeArr = ['XSM_CZDL1', 'XSM_CZLS1', 'XSM_CZSW1', 'XSM_CZZZ1', 'XSM_SDHK', 'XSM_ZSZK', 'XSM_JXDK10', 'XSM_JXSHK10', 'XSM_JXSK10']; //小四门相关课程大类
export const XKTAndXSMCurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr]; //学考通、小四门所有课程大类
export const CurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr]; //学考通、小四门所有课程大类
export const MATHCurriculumCodeArr = ['MATH', 'MATH_HKB', 'MATH_BSD', 'MATH_ZXK', 'MATH_STK']; //数学相关课程大类

// export const baseUrl = 'https://test2-k8s.ngrok.dxznjy.com/';
// export const baseUrl = "https://uat-gateway.dxznjy.com/";
// export const baseUrl = "https://cdw.ngrok.dxznjy.com";
// export const baseUrl = "https://testpay.ngrok.dxznjy.com/";
// export const baseUrl = 'https://test-k8s.ngrok.dxznjy.com/';
// export const baseUrl = "https://ghtt.ngrok.dxznjy.com/";
// export const baseUrl = "https://applet.dxznjy.com";
// export const baseUrl = "https://sit1.ngrok.dxznjy.com";
// export const baseUrl = 'https://test176.ngrok.dxznjy.com';
// export const baseUrl = 'https://lvhaifeng.ngrok.dxznjy.com';
// export const baseUrl = "https://pyfdev.ngrok.dxznjy.com";//一诚
// export const baseUrl = 'https://test2-gateway.dxznjy.com';
// export const baseUrl = 'https://test177.ngrok.dxznjy.com';
// export const baseUrl = 'https://sit1.ngrok.dxznjy.com';
// export const baseUrl = 'http://lvhaifeng.ngrok.dxznjy.com';
// 线上
// export const baseUrl = 'https://gateway.dxznjy.com';
// export const baseUrl = 'https://test176.ngrok.dxznjy.com';
// export const baseUrl = 'https://lpy.ngrok.dxznjy.com';
export const baseUrl = 'https://gateway.dxznjy.com';

// export const baseUrl = 'https://linetest.dxznjy.com'; // 生产

export const root = {
  rval: 'root',
  pval: '*'
};
