<!-- 交付中心-一对多学员管理-待完善上课信息表 -->
<template>
  <div>
    <!-- 课程类型 -->
    <el-row style="margin: 20px 0 20px 20px">
      <el-radio-group v-model="courseType" size="medium" @change="handleCourseTypeTabsClick">
        <el-radio-button label="1">试课</el-radio-button>
        <el-radio-button label="2">正式课</el-radio-button>
        <el-radio-button label="3">补课</el-radio-button>
      </el-radio-group>
    </el-row>

    <!-- 试课 -->
    <PendingCompletionClassInfoTrial v-if="courseType === '1'" />

    <!-- 正式课 -->
    <PendingCompletionClassInfoFormal v-if="courseType === '2'" />

    <!-- 补课-->
    <PendingCompletionClassInfoMissed v-if="courseType === '3'" />
  </div>
</template>
<script>
  import PendingCompletionClassInfoTrial from './PendingCompletionClassInfoTrial.vue';
  import PendingCompletionClassInfoFormal from './PendingCompletionClassInfoFormal.vue';
  import PendingCompletionClassInfoMissed from './PendingCompletionClassInfoMissed.vue';
  export default {
    name: 'PendingCompletionClassInfo',
    components: {
      PendingCompletionClassInfoMissed,
      PendingCompletionClassInfoTrial,
      PendingCompletionClassInfoFormal
    },
    data() {
      return {
        courseType: '1'
      };
    },
    methods: {
      handleCourseTypeTabsClick(value) {
        this.courseType = value;
      }
    }
  };
</script>

<style lang="scss" scoped></style>
