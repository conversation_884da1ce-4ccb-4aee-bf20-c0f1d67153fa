import Vue from 'vue';

import 'normalize.css/normalize.css'; // A modern alternative to CSS resets

import Element from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import 'element-ui/lib/theme-chalk/display.css';

import '@/styles/index.scss'; // global css
import store from './store';
import App from './App';
import router from './router';
import print from './plugs/print'; // 打印机
import './icons'; // icon
import './errorLog'; // error log
import './permission'; // permission control
import * as filters from './filters'; // global filters
import VueAMap from 'vue-amap'; // 高德地图
import * as moment from 'moment';
import dayjs from 'dayjs';
// import moment from 'moment'//导入文件

import captcha from 'vue-social-captcha';
import { VuePlugin } from 'vuera';
// 权限指令
import hasPerm from '@/directive/permission/hasPerm';
import perm from '@/directive/permission/perm';
import '@/filters/timeDown';
// import '@/components/Dialog';
// import DateRange from '@/components/DateRange';

// import loadmore from '@/utils/directives/loadmore'
// Vue.use(loadmore)
// 图片懒加载
import lazyLoad from './directive/lazy';
Vue.directive('lazy', lazyLoad);
// 触底加载
import selectLoadmore from './directive/select-loadmore/index.js';
Vue.directive('el-select-loadmore', selectLoadmore);
// Vue.component('date-range', DateRange);

// 注册全局的权限判断方法和指令
Vue.prototype.$hasPerm = hasPerm;
Vue.prototype.$dayjs = dayjs;
Vue.prototype.$moment = moment;
moment.locale('zh-cn'); //需要汉化

Vue.directive('perm', perm);

Vue.use(Element, {
  size: 'small' // set element-ui default size
});
// Vue.use(VuePlugin)
// register global utility filters.
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});
if (process.env.NODE_ENV === 'production') {
  if (window) {
    window.console.log = function () { };
    window.console.log = function () { };
  }
}
// import htmlToPdf from '@/utils/htmlToPdf'
// Vue.use(htmlToPdf)

Vue.use(VuePlugin);
Vue.use(captcha);
Vue.use(VueAMap); //插件使用声明
//下面是vue-amap初始化，请将AMapKey换成你自己的key
VueAMap.initAMapApiLoader({
  key: 'dbd0d5d74d4fd5a36a6c43f56acc300f',
  plugin: [
    'AMap.Autocomplete',
    'AMap.PlaceSearch',
    'AMap.Scale',
    'AMap.OverView',
    'AMap.ToolBar',
    'AMap.MapType',
    'AMap.PolyEditor',
    'AMap.Geolocation',
    'AMap.Geocoder',
    'AMap.CircleEditor'
  ],
  v: '1.4.4'
});
Vue.config.productionTip = false;
Vue.prototype.aliUrl = 'https://document.dxznjy.com/'; //阿里上传图片后展示根目录
Vue.use(print);
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
});
