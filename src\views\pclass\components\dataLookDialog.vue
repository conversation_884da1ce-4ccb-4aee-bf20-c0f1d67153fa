<template>
  <div class="dialog">
    <div v-if="TeacherList.curriculumName == '拼音法' || TeacherList.curriculumName == '拼音法（高年级）'">
      <el-drawer
        :title="TeacherList.curriculumName + '体验结果反馈'"
        :visible="childVisible_"
        :direction="direction"
        @close="handleClose"
        style="margin-top: 15px"
        :size="screenWidth > 1300 ? '35%' : '70vw'"
      >
        <el-row class="paikeTwo" style="margin-top: 1vw">
          <el-col class="paike">
            日期：
            <span>{{ TeacherList.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ TeacherList.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ TeacherList.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ TeacherList.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            时间：
            <span>{{ TeacherList.studyTime }}</span>
          </el-col>
          <el-col class="paike">
            实际时间：
            <span>{{ TeacherList.actualStart }} 至 {{ TeacherList.actualEnd }}</span>
          </el-col>
          <el-col class="paike">
            学习学时：
            <span>{{ TeacherList.studyHour + '个小时' }}</span>
          </el-col>
          <div class="paike" v-if="TeacherList.extendProperty.totalCourseHours">已购拼音法课时：{{ TeacherList.extendProperty.totalCourseHours }}个小时</div>
          <div class="paike" v-if="TeacherList.extendProperty.totalCourseHours">剩余拼音法课时：{{ TeacherList.extendProperty.haveCourseHours }}个小时</div>
          <el-col class="paike">
            所学课程类型：
            <span>{{ TeacherList.curriculumName }}{{ TeacherList.extendProperty.courseTypeName }}</span>
          </el-col>
          <el-col class="paike">
            所学课程名称：
            <span>{{ TeacherList.extendProperty.courseNames }}</span>
          </el-col>
          <el-col class="paike">
            学习元辅音个数：
            <span>{{ TeacherList.extendProperty.consonantCounts }}</span>
          </el-col>
          <el-col class="paike">
            学习音节个数：
            <span>{{ TeacherList.extendProperty.syllableCounts }}</span>
          </el-col>
          <el-col class="paike">
            学习单词个数：
            <span>{{ TeacherList.extendProperty.wordCounts }}</span>
          </el-col>
          <el-col class="paike">
            <div style="margin-bottom: 8px">教练评语：</div>
            <el-input type="textarea" autosize :autosize="{ minRows: 3, maxRows: 5 }" v-model="TeacherList.feedback" disabled></el-input>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
    <div v-else-if="TeacherList.moduleType == 4">
      <el-drawer
        title="全能听力体验结果反馈"
        :visible="childVisible_"
        :direction="direction"
        @close="handleClose"
        style="margin-top: 15px"
        :size="screenWidth > 1300 ? '35%' : '70vw'"
      >
        <el-row class="paikeTwo" style="margin-top: 1vw">
          <el-col class="paike">
            日期：
            <span>{{ TeacherList.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ TeacherList.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ TeacherList.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ TeacherList.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            实际时间：
            <span>{{ TeacherList.actualStart }} 至 {{ TeacherList.actualEnd }}</span>
          </el-col>
          <el-col class="paike">
            学习学时：
            <span>{{ TeacherList.studyHour + '个小时' }}</span>
          </el-col>
          <el-col class="paike">
            课程类型：
            <span>{{ TeacherList.listeningStatisticsDto.courseName }}</span>
          </el-col>
          <el-col class="paike">
            课程名称：
            <span>{{ listenName(TeacherList.listeningStatisticsDto.courseList) }}</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px">
            <div>学习进度：{{ listenProgress(TeacherList.listeningStatisticsDto.progressList) }}</div>
          </el-col>
          <el-col class="paike">
            听力名称(正确率)：
            <span>{{ listenArray(TeacherList.listeningStatisticsDto.listeningList) }}</span>
          </el-col>
          <el-col class="paike">
            体验后学习意愿：
            <span>{{ TeacherList.studyIntention }}</span>
          </el-col>
          <el-col class="paike">
            <div style="margin-bottom: 8px">教练评语：</div>
            <el-input type="textarea" autosize :autosize="{ minRows: 3, maxRows: 5 }" v-model="TeacherList.feedback" disabled></el-input>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
    <div v-else>
      <el-drawer
        :title="TeacherList.curriculumName + '体验结果反馈'"
        :visible="childVisible_"
        :direction="direction"
        @close="handleClose"
        style="margin-top: 15px"
        :size="screenWidth > 1300 ? '35%' : '70vw'"
      >
        <el-row class="paikeTwo" style="margin-top: 1vw">
          <el-col class="paike">
            日期：
            <span>{{ TeacherList.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ TeacherList.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ TeacherList.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ TeacherList.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            教练：
            <span>{{ TeacherList.teacherName }}</span>
          </el-col>
          <el-col class="paike" v-if="XKTCurriculumCodeArr.includes(TeacherList.curriculumCode)">
            授课视频：
            <div style="padding-right: 50px; word-break: break-all; overflow-wrap: break-word" v-for="item in TeacherList.xktStatisticsDto.courseGradeVideos" :key="item.id">
              {{ item.gradeName }}/{{ item.courseName }}/{{ item.videoName }}
            </div>
          </el-col>
          <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
            试学学时：
            <span>{{ TeacherList.studyHour + '/时' }}</span>
          </el-col>
          <template v-if="TeacherList.moduleType != 3">
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              上学时间：
              <span>{{ TeacherList.studyTime }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              词汇测试水平：
              <span>{{ !!TeacherList.vocabularyLevel ? TeacherList.vocabularyLevel : '未测试' }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              首测词汇量：
              <span>{{ !!TeacherList.expWords ? TeacherList.expWords : '未测试' }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              识记词汇数量：
              <span>{{ TeacherList.todayWords }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              遗忘数量：
              <span>{{ TeacherList.forgetWords }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              记忆率：
              <span>{{ !!TeacherList.wordsRate ? TeacherList.wordsRate + '%' : '' }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              体验词库：
              <span>{{ TeacherList.studyBooks }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              记忆情况：
              <span>{{ TeacherList.memoryTime }}分钟记住{{ TeacherList.memoryNum }}个单词</span>
            </el-col>
          </template>
          <template v-if="TeacherList.moduleType == 3">
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              时间：
              <span>{{ TeacherList.studyTime }}</span>
            </el-col>
            <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
              实际时间：
              <span>{{ TeacherList.actualStart + '--' + TeacherList.actualEnd }}</span>
            </el-col>
            <el-col class="paike">
              所学课程类型：
              <span>{{ TeacherList.superReadCourseStatistics.courseName }}</span>
            </el-col>
            <el-col class="paike">
              <el-col :span="4">学习进度：</el-col>
              <el-col :span="20" style="white-space: pre-line">
                <div>{{ TeacherList.superReadCourseStatistics.learningProgress + '%' }}</div>
              </el-col>
            </el-col>
            <el-col class="paike" style="padding-right: 50px">
              <div>学习关卡(正确率)：{{ rateMapArray(TeacherList.superReadCourseStatistics.checkpointList) }}</div>
            </el-col>
            <el-col class="paike" style="padding-right: 50px">
              <div>所学课程名称进度：{{ mapArray(TeacherList.superReadCourseStatistics.courseList) }}</div>
            </el-col>
          </template>
          <el-col class="paike" v-if="TeacherList.curriculumName == '鼎英语'">
            体验后学习意愿：
            <span>{{ TeacherList.studyIntention }}</span>
          </el-col>
          <el-col class="paike" v-if="XKTCurriculumCodeArr.includes(TeacherList.curriculumCode)">
            <!-- 课堂实时监测表格反馈 和 教学效果观察登记表 -->
            <!-- <div v-for="item in feedbackTableData" :key="item.label">
              <div class="feedback-title">{{ item.label }}</div>
              <div class="paike-item">
                <div v-for="info in item.list" :key="info.name">
                  <div class="feedback-title">{{ info.label + '-' + info.name }}：{{ checkSelect(TeacherList.xktFeedbackTable[info.selectKey]) }}</div>
                  <div v-if="info.notes" class="feedback-title">{{ info.label + '-' + info.name }}：{{ TeacherList.xktFeedbackTable[info.notes] }}</div>
                </div>
              </div>
            </div> -->
            <div>反馈表格：</div>
            <!-- 课堂实时监测 -->
            <el-table :data="feedbackTableData[0].list" border :span-method="spanMethod0" :header-cell-style="{ fontWeight: 'bold', fontSize: '15px' }">
              <el-table-column prop="address" :label="feedbackTableData[0].label" label-class-name="monitor-header">
                <el-table-column label="维度" width="100px">
                  <template v-slot="{ row }">
                    <div style="font-weight: bold; font-size: 14px">
                      {{ row.label }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="观察要点">
                  <template v-slot="{ row }">{{ row.name }}</template>
                </el-table-column>
                <el-table-column label="记录符号（ √ / × / 无 ）">
                  <template v-slot="{ row }">
                    <div>{{ checkSelect(TeacherList.xktFeedbackTable[row.selectKey]) }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="备注（详细记录）">
                  <template v-slot="{ row }">
                    <div>{{ TeacherList.xktFeedbackTable[row.notes] }}</div>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>

            <!-- 教学效果观察 -->
            <el-table :data="feedbackTableData[1].list" border :span-method="spanMethod1" :header-cell-style="{ fontWeight: 'bold', fontSize: '15px' }">
              <el-table-column prop="address" :label="feedbackTableData[1].label" label-class-name="monitor-header">
                <el-table-column label="维度" width="100px">
                  <template v-slot="{ row }">
                    <div style="font-weight: bold; font-size: 14px">
                      {{ row.label }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="观察要点">
                  <template v-slot="{ row }">{{ row.name }}</template>
                </el-table-column>
                <el-table-column label="记录符号（ √ / × / 无 ）">
                  <template v-slot="{ row }">
                    <div>{{ checkSelect(TeacherList.xktFeedbackTable[row.selectKey]) }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="备注（详细记录）">
                  <template v-slot="{ row }">
                    <div>{{ TeacherList.xktFeedbackTable[row.notes] }}</div>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col class="paike">
            <div style="margin-bottom: 8px">学员学习状况反馈：</div>
            <el-input type="textarea" autosize :autosize="{ minRows: 3, maxRows: 5 }" v-model="TeacherList.feedback" disabled></el-input>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
  </div>
</template>

<script>
  import { getCourseInfo } from '@/api/studentClass/changeList';
  import { XKTCurriculumCodeArr } from '@/utils/constants';
  import feedbackTableData from '@/utils/xktFeedbackTableData';

  export default {
    name: 'dataLookDialog',
    props: {
      // 控制弹窗显示
      dataLookerStyle: {
        type: Boolean,
        default: false //这里默认为false
      }
    },
    data() {
      return {
        feedbackTableData: feedbackTableData,
        screenWidth: window.screen.width,
        experienceId: '',
        value: '',
        tableData: '',
        itemcount: 1,
        drawer: false,
        direction: 'rtl',
        teacherNum: {
          id: '',
          teacherId: ''
        },
        teacher: '',
        TeacherList: [],
        timer: '',
        val: '',
        pickerOptions: {
          disabledDate: this.disabledDate
        },
        XKTCurriculumCodeArr: XKTCurriculumCodeArr
      };
    },
    mounted() {
      if (this.role == 3) {
        this.chartY = '9.5%';
      } else {
        this.chartY = '18%';
      }
    },
    computed: {
      childVisible_: {
        get() {
          return this.dataLookerStyle;
        },
        //值变化的时候会被调用
        set(v) {
          this.$emit('dataLooker', false);
        }
      }
    },
    created() {},
    methods: {
      // 处理学考通-填写反馈表格相同列合并
      spanMethod({ row, column, rowIndex, columnIndex }, index) {
        // 只处理第一列（维度列）
        if (columnIndex === 0) {
          // 获取当前行的label值
          const currentLabel = row.label;

          // 查找相同label的连续行数
          let rowspan = 1;
          for (let i = rowIndex + 1; i < this.feedbackTableData[index].list.length; i++) {
            if (this.feedbackTableData[index].list[i].label === currentLabel) {
              rowspan++;
            } else {
              break;
            }
          }

          // 处理连续标签的首行
          /**
           * 如果
           */
          // console.log('🚀 ~ spanMethod ~ rowspan:', rowspan);
          if (rowIndex === 0 || this.feedbackTableData[index].list[rowIndex - 1].label !== currentLabel) {
            return {
              rowspan: rowspan,
              colspan: 1
            };
          }
          // 连续标签的非首行，隐藏单元格
          else {
            return { rowspan: 0, colspan: 0 };
          }
        }
      },
      // 处理学考通-填写反馈表格相同列合并
      spanMethod0(row) {
        return this.spanMethod(row, 0);
      },
      spanMethod1(row) {
        return this.spanMethod(row, 1);
      },
      // 处理学考通-反馈表格-判断选择
      checkSelect(value) {
        switch (value) {
          case 1:
            return '√';
          case 2:
            return '×';
          case 3:
            return '无';

          default:
            return '空';
        }
      },
      rateMapArray(arr) {
        if (arr && arr.length > 0) {
          let newArr = [];
          newArr = arr.map((i) => {
            let str = '';
            let accuracyRate = i.accuracyRate ? i.accuracyRate + '%' : '-';
            let checkpointName = i.checkpointName;
            str = `${checkpointName}(${accuracyRate})`;
            //arr.push(str)
            return str;
          });
          return newArr.join(',');
          //return arr.map((i) => i.checkpointName + '（' + i.accuracyRate + '%' + '）').join(',');
        } else {
          return '-';
        }
      },
      mapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.courseName + '（' + i.checkpointNum + '）').join(' , ');
        } else {
          return '-';
        }
      },
      //听力数据处理
      listenProgress(progressList) {
        return progressList.map((item) => `${parseFloat(item.learningProgress)}%`).join(' , ');
      },
      listenName(progressList) {
        return progressList.map((item) => item.courseName).join(' ');
      },
      listenArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.listeningName + '（' + i.accuracyRate + '%）').join(' , ');
        } else {
          return '-';
        }
      },
      // 获取数据
      initData() {
        getCourseInfo(this.experienceId).then((res) => {
          this.TeacherList = res.data;
          console.log(this.TeacherList);
          this.dataLookerStyle = true;
          if (this.TeacherList.memory == 0) {
            this.TeacherList.memory = '暂无';
          }
        });
      },
      disabledDate(time) {
        // time 是new Date
        return time.getTime() < Date.now() - 3600 * 1000 * 24;
      },
      teacherType() {
        if (this.coniditon.teachingType === '1') {
          return '男';
        } else if (this.coniditon.teachingType === '2') {
          return '女';
        }
      },
      // 关闭弹窗
      handleClose() {
        this.childVisible_ = false;
        this.dataLookerStyle = false;
        // this.details=''
      },
      quxiaoBtn() {
        this.childVisible_ = true;
        this.dataLookerStyle = true;
      },
      async quedingBtn() {
        this.childVisible_ = false;
        this.dataLookerStyle = false;
        //重新获取列表
        this.$emit('updateList');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .diyih {
    display: flex;
    justify-content: center;
    margin-bottom: 3vh;
  }

  .firste {
    font-size: 18px;
    font-weight: 900;
    margin-left: 5vw;
  }

  .Second {
    padding-left: 60px;
  }

  // 2行
  .dierr {
    display: flex;
    justify-content: space-between;
  }

  .nob {
    border: none;
    background: #fff;

    &:first-child {
      padding-left: 3vw;
    }

    &:last-child {
      padding-right: 3vw;
    }
  }

  .paike {
    margin-bottom: 20px;
    margin-left: 2vw;
    width: calc(100% - 2vw);

    &:first-child {
      margin-top: 0.5vw;
    }
  }

  .paike-item {
    margin: 10px;

    & > div {
      margin-bottom: 5px;
    }
  }

  .paikeTwo {
    width: 90%;
  }

  .xubtn {
    margin-top: 10vh;
  }

  .cc {
    height: 0px;
    margin: 1vh 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }

  .active1 {
    font-weight: 900;
    font-size: 18px;
    border-bottom: 2px solid #000;
  }

  .dateArrClass > div ::after {
    content: '';
    position: absolute;
    right: 9px;
    top: 21px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #fc3c39;
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-radius: 25px;
    height: 100%;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    // font-size: 22px;
    font-size: 30px; // 拼音法
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
    padding-top: 0;
    margin-top: 15px;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
  }

  ::v-deep {
    // 主列头样式
    .monitor-header {
      background-color: #4863ad !important;
      color: #fff;
      font-weight: bold;
      font-size: 15px;
    }
    .el-table .cell {
      word-break: break-word;
    }
  }
</style>
